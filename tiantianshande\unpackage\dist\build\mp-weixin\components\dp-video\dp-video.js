(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/dp-video/dp-video"],{"06aa":function(t,i,o){},3195:function(t,i,o){"use strict";var e=o("06aa"),n=o.n(e);n.a},"359f":function(t,i,o){"use strict";o.r(i);var e=o("ac7f"),n=o.n(e);for(var s in e)["default"].indexOf(s)<0&&function(t){o.d(i,t,(function(){return e[t]}))}(s);i["default"]=n.a},"5af36":function(t,i,o){"use strict";o.r(i);var e=o("e5be"),n=o("359f");for(var s in n)["default"].indexOf(s)<0&&function(t){o.d(i,t,(function(){return n[t]}))}(s);o("3195");var a=o("828b"),c=Object(a["a"])(n["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);i["default"]=c.exports},ac7f:function(t,i,o){"use strict";(function(t){Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var o={data:function(){return{videoHeight:180,screenWidth:375,isPlaying:!1,videoObjectFit:"cover",videoAspectRatio:16/9}},computed:{showPoster:function(){return!!this.params.pic&&(!this.params.autoplay&&!this.isPlaying)}},props:{params:{},data:{}},mounted:function(){var i=this;this.initVideoHeight(),t.onWindowResize((function(){i.initVideoHeight()})),console.log("视频组件参数:",this.params),console.log("自动播放设置:",this.params.autoplay),console.log("静音设置:",this.params.muted),console.log("循环设置:",this.params.loop),this.params.autoplay&&(console.log("准备自动播放视频"),this.$nextTick((function(){setTimeout((function(){i.tryAutoPlay()}),800)})))},methods:{initVideoHeight:function(){var i=t.getSystemInfoSync();this.screenWidth=i.screenWidth,this.videoHeight=Math.floor(this.screenWidth/16*9);var o=Math.floor(.75*this.screenWidth);this.videoHeight<120?this.videoHeight=120:this.videoHeight>o&&(this.videoHeight=o)},playVideo:function(){this.isPlaying=!0;var i=t.createVideoContext("myVideo",this);i.play()},onPlay:function(){this.isPlaying=!0},onPause:function(){},onEnded:function(){this.params.loop||(this.isPlaying=!1)},tryAutoPlay:function(){var i=this;if(console.log("tryAutoPlay 被调用"),console.log("autoplay:",this.params.autoplay),console.log("src:",this.params.src),this.params.autoplay&&this.params.src){console.log("开始尝试自动播放");try{var o=t.createVideoContext("myVideo",this);if(console.log("videoContext:",o),o){this.params.muted&&console.log("视频设置为静音播放"),console.log("调用 videoContext.play()");var e=o.play();e&&"function"===typeof e.then?e.then((function(){console.log("自动播放成功"),i.isPlaying=!0})).catch((function(t){console.log("自动播放被浏览器阻止:",t),i.isPlaying=!1})):(this.isPlaying=!0,console.log("自动播放已启动（无Promise返回）"))}else console.log("无法获取视频上下文")}catch(n){console.log("自动播放失败，可能被浏览器阻止:",n),this.isPlaying=!1}}else console.log("自动播放条件不满足")},onLoadedMetadata:function(t){var i=t.detail;if(i&&i.width&&i.height){this.videoAspectRatio=i.width/i.height;var o=this.screenWidth/this.videoHeight,e=Math.abs(this.videoAspectRatio-o);this.videoObjectFit=e<.05?"fill":"cover";var n=Math.floor(this.screenWidth/this.videoAspectRatio),s=Math.floor(.75*this.screenWidth);n>=120&&n<=s&&(this.videoHeight=n)}}}};i.default=o}).call(this,o("df3c")["default"])},e5be:function(t,i,o){"use strict";o.d(i,"b",(function(){return e})),o.d(i,"c",(function(){return n})),o.d(i,"a",(function(){}));var e=function(){var t=this.$createElement;this._self._c},n=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/dp-video/dp-video-create-component',
    {
        'components/dp-video/dp-video-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("5af36"))
        })
    },
    [['components/dp-video/dp-video-create-component']]
]);

<template>
<view class="dp-video" :style="{
	backgroundColor: params.bgcolor,
	backgroundImage: params.bg_image ? `url(${params.bg_image})` : 'none',
	backgroundSize: 'cover',
	backgroundPosition: 'center',
	margin: (params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx 0',
	padding: (params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',
	borderRadius: params.bg_border_radius ? params.bg_border_radius + 'rpx' : '0'
}">
	<view class="poster-container" v-if="showPoster"
		:style="{
			backgroundImage: `url(${params.pic})`,
			height: videoHeight + 'px',
			borderRadius: params.border_radius ? params.border_radius + 'rpx' : '0'
		}"
		@click="playVideo">
		<view class="play-icon"></view>
	</view>
	<video
		class="dp-video-video"
		:src="params.src"
		:poster="params.pic"
		:controls="params.controls !== false"
		:autoplay="params.autoplay && !showPoster"
		:muted="params.muted"
		:loop="params.loop"
		:object-fit="params.fillMode || videoObjectFit"
		:style="{
			height: videoHeight + 'px',
			borderRadius: params.border_radius ? params.border_radius + 'rpx' : '0',
			display: showPoster ? 'none' : 'block'
		}"
		@play="onPlay"
		@pause="onPause"
		@ended="onEnded"
		@loadedmetadata="onLoadedMetadata"
		id="myVideo"
	></video>
</view>
</template>

<script>
export default {
	data() {
		return {
			videoHeight: 180,
			screenWidth: 375,
			isPlaying: false,
			videoObjectFit: 'cover', // 默认使用cover填满容器，消除黑边
			videoAspectRatio: 16/9 // 默认视频比例
		}
	},
	computed: {
		// 计算是否显示封面
		showPoster() {
			// 如果没有封面图片，不显示封面
			if (!this.params.pic) {
				return false
			}
			// 如果开启了自动播放，不显示封面（让视频直接播放）
			if (this.params.autoplay) {
				return false
			}
			// 如果正在播放，不显示封面
			if (this.isPlaying) {
				return false
			}
			// 其他情况显示封面
			return true
		}
	},
	props: {
		params: {},
		data: {}
	},
	mounted() {
		this.initVideoHeight()
		uni.onWindowResize(() => {
			this.initVideoHeight()
		})

		// 调试信息：打印视频参数
		console.log('视频组件参数:', this.params)
		console.log('自动播放设置:', this.params.autoplay)
		console.log('静音设置:', this.params.muted)
		console.log('循环设置:', this.params.loop)

		// 如果开启了自动播放，延迟一点时间后尝试播放
		if (this.params.autoplay) {
			console.log('准备自动播放视频')
			this.$nextTick(() => {
				setTimeout(() => {
					this.tryAutoPlay()
				}, 800) // 增加延迟到800ms确保视频元素已经完全渲染
			})
		}
	},
	methods: {
		initVideoHeight() {
			const systemInfo = uni.getSystemInfoSync()
			this.screenWidth = systemInfo.screenWidth

			// 使用更保守的高度计算，优先消除黑边
			// 对于大多数视频，使用16:9比例作为基准
			this.videoHeight = Math.floor(this.screenWidth / 16 * 9)

			// 设置合理的高度范围
			const minHeight = 120 // 降低最小高度
			const maxHeight = Math.floor(this.screenWidth * 0.75) // 稍微降低最大高度

			if (this.videoHeight < minHeight) {
				this.videoHeight = minHeight
			} else if (this.videoHeight > maxHeight) {
				this.videoHeight = maxHeight
			}
		},
		playVideo() {
			this.isPlaying = true
			// 获取视频上下文
			const videoContext = uni.createVideoContext('myVideo', this)
			// 播放视频
			videoContext.play()
		},
		onPlay() {
			this.isPlaying = true
		},
		onPause() {
			// 暂停时不改变播放状态，以免影响用户体验
		},
		onEnded() {
			// 如果没有开启循环播放，则标记为停止播放
			if (!this.params.loop) {
				this.isPlaying = false
			}
		},
		tryAutoPlay() {
			// 尝试自动播放视频
			console.log('tryAutoPlay 被调用')
			console.log('autoplay:', this.params.autoplay)
			console.log('src:', this.params.src)

			if (this.params.autoplay && this.params.src) {
				console.log('开始尝试自动播放')
				try {
					const videoContext = uni.createVideoContext('myVideo', this)
					console.log('videoContext:', videoContext)

					if (videoContext) {
						// 如果设置了静音，先设置静音
						if (this.params.muted) {
							console.log('视频设置为静音播放')
							// 注意：uni-app的video组件静音通过muted属性控制
						}

						// 尝试播放
						console.log('调用 videoContext.play()')
						const playPromise = videoContext.play()

						// 处理播放Promise（如果存在）
						if (playPromise && typeof playPromise.then === 'function') {
							playPromise.then(() => {
								console.log('自动播放成功')
								this.isPlaying = true
							}).catch((error) => {
								console.log('自动播放被浏览器阻止:', error)
								this.isPlaying = false
							})
						} else {
							// 对于不返回Promise的情况
							this.isPlaying = true
							console.log('自动播放已启动（无Promise返回）')
						}
					} else {
						console.log('无法获取视频上下文')
					}
				} catch (error) {
					console.log('自动播放失败，可能被浏览器阻止:', error)
					// 自动播放失败时，显示封面让用户手动点击
					this.isPlaying = false
				}
			} else {
				console.log('自动播放条件不满足')
			}
		},
		onLoadedMetadata(e) {
			// 当视频元数据加载完成时，获取视频的真实尺寸
			const detail = e.detail
			if (detail && detail.width && detail.height) {
				// 计算视频的真实宽高比
				this.videoAspectRatio = detail.width / detail.height

				// 优先使用cover来消除黑边，除非视频比例与容器完全匹配
				const containerAspectRatio = this.screenWidth / this.videoHeight
				const aspectRatioDiff = Math.abs(this.videoAspectRatio - containerAspectRatio)

				if (aspectRatioDiff < 0.05) {
					// 比例非常接近时使用fill完全填满
					this.videoObjectFit = 'fill'
				} else {
					// 其他情况都使用cover来消除黑边
					this.videoObjectFit = 'cover'
				}

				// 根据视频真实比例重新计算最佳高度
				const idealHeight = Math.floor(this.screenWidth / this.videoAspectRatio)
				const minHeight = 120
				const maxHeight = Math.floor(this.screenWidth * 0.75)

				if (idealHeight >= minHeight && idealHeight <= maxHeight) {
					this.videoHeight = idealHeight
				}
			}
		}
	}
}
</script>

<style>
.dp-video {
	height: auto;
	position: relative;
	font-size: 0;
	/* border-radius: 8px; */
	overflow: hidden;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
	background-color: #000; /* 添加黑色背景，避免加载时的白色闪烁 */
}

.dp-video-video {
	width: 100%;
	margin: 0px;
	padding: 0px;
	transition: height 0.3s ease;
	/* 移除固定的object-fit，改为动态设置 */
	background-color: #000; /* 视频背景色设为黑色 */
	/* 删除这里的固定圆角，改为动态样式 */
	/* 强制视频填满容器，消除黑边 */
	object-position: center;
}

.poster-container {
	width: 100%;
	background-size: cover;
	background-position: center;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #000; /* 封面容器背景色 */
}

.play-icon {
	width: 60px;
	height: 60px;
	background-color: rgba(255, 255, 255, 0.8); /* 改为白色半透明背景，更醒目 */
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	transition: all 0.3s ease; /* 添加过渡效果 */
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3); /* 添加阴影 */
}

.play-icon:hover {
	background-color: rgba(255, 255, 255, 0.9); /* 悬停效果 */
	transform: scale(1.1); /* 悬停时稍微放大 */
}

.play-icon:after {
	content: '';
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 12px 0 12px 20px;
	border-color: transparent transparent transparent #333; /* 改为深色三角形 */
	position: absolute;
	left: 22px;
}
</style>
<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

namespace app\common;
use think\facade\Db;
class System
{
	//网站名称
	public static function webname(){
		$webinfo = Db::name('sysset')->where(['name'=>'webinfo'])->value('value');
		$webinfo = json_decode($webinfo,true);
		$webname = $webinfo['webname'];
		return $webname;
	}
	//公众号或小程序 平台信息
	public static function appinfo($aid,$platform){
		if(!$platform) $platform = 'mp';
		$appinfo = Db::name('admin_setapp_'.$platform)->where(['aid'=>$aid])->find();
		return $appinfo;
	}
	//操作日志记录
	public static function plog($remark,$aid=0){
		if($aid){
			Db::name('plog')->insert(['aid'=>$aid,'bid'=>0,'uid'=>1,'remark'=>$remark,'createtime'=>time()]);
		}else{
			if($remark == '系统升级'){
				Db::name('plog')->insert(['aid'=>1,'bid'=>0,'uid'=>1,'remark'=>$remark,'createtime'=>time()]);
			}else{
				Db::name('plog')->insert(['aid'=>aid,'bid'=>bid,'uid'=>uid,'remark'=>$remark,'createtime'=>time()]);
			}
		}
	}

	public static function initaccount($aid){
		$admin_set = Db::name('admin_set')->where('aid',$aid)->find();
		if(!$admin_set){
			Db::name('admin_set')->insert([
				'aid'=>$aid,
				'name'=>'商城系统',
				'logo'=>PRE_URL.'/static/imgsrc/logo.jpg'
			]);
			$sysset = Db::name('admin_set')->where('aid',$aid)->find();

			Db::name('mendian')->insert(['aid'=>$aid,'name'=>'总店','address'=>'北京天安门广场','pic'=>PRE_URL.'/static/imgsrc/picture-1.jpg','longitude'=>'116.**************','latitude'=>'39.***********','createtime'=>time()]);


			Db::name('admin_setapp_mp')->insert(['aid'=>$aid]);
			Db::name('admin_setapp_wx')->insert(['aid'=>$aid]);
			Db::name('admin_setapp_alipay')->insert(['aid'=>$aid]);
			Db::name('admin_setapp_baidu')->insert(['aid'=>$aid]);
			Db::name('admin_setapp_toutiao')->insert(['aid'=>$aid]);
			Db::name('admin_setapp_qq')->insert(['aid'=>$aid]);
			Db::name('admin_setapp_h5')->insert(['aid'=>$aid]);
			Db::name('admin_setapp_app')->insert(['aid'=>$aid]);

			Db::name('freight')->insert([
				'aid'=>$aid,
				'name'=>'普通快递',
				'pstype'=>0,
				'pricedata'=>'[{"region":"全国(默认运费)","fristweight":"1000","fristprice":"0","secondweight":"1000","secondprice":"0"}]',
				'pstimedata'=>'[{"day":"1","hour":"12","minute":"0","hour2":"12","minute2":"30"},{"day":"1","hour":"18","minute":"0","hour2":"18","minute2":"30"},{"day":"2","hour":"12","minute":"0","hour2":"12","minute2":"30"},{"day":"2","hour":"18","minute":"0","hour2":"18","minute2":"30"}]',
				'status'=>1,
			]);
			Db::name('freight')->insert([
				'aid'=>$aid,
				'name'=>'到店自提',
				'pstype'=>1,
				'pricedata'=>'[{"region":"全国(默认运费)","fristweight":"1000","fristprice":"0","secondweight":"1000","secondprice":"0"}]',
				'pstimedata'=>'[{"day":"1","hour":"12","minute":"0","hour2":"12","minute2":"30"},{"day":"1","hour":"18","minute":"0","hour2":"18","minute2":"30"},{"day":"2","hour":"12","minute":"0","hour2":"12","minute2":"30"},{"day":"2","hour":"18","minute":"0","hour2":"18","minute2":"30"}]',
				'status'=>1,
			]);

			Db::name('member_level')->insert([
				'aid'=>$aid,
				'sort'=>1,
				'isdefault'=>1,
				'name'=>'普通会员',
				'icon'=>PRE_URL.'/static/imgsrc/level_1.png',
				'explain'=>'<p>1、享受消费送积分，积分可以抵扣金额、兑换礼品、参与抽奖活动；</p><p>2、购买商品享受商城优惠价格；</p><p>3、免费领取优惠券、购买商品直接抵扣商品金额；</p><p>4、更多优惠活动请随时关注平台更新信息。</p>'
			]);
			$level2id = Db::name('member_level')->insertGetId([
				'aid'=>$aid,
				'sort'=>2,
				'isdefault'=>0,
				'name'=>'分销商',
				'icon'=>PRE_URL.'/static/imgsrc/level_2.png',
				'can_apply'=>1,
				'apply_check'=>1,
				'can_agent'=>3,
				'commission1'=>3,
				'commission2'=>2,
				'commission3'=>1,
				'explain'=>'<p>1、享受消费送积分，积分可以抵扣金额、兑换礼品、参与抽奖活动；</p><p>2、购买商品享受商城优惠价格；</p><p>3、免费领取优惠券、购买商品直接抵扣商品金额；</p><p>4、分享商品给好友购买，可获得佣金奖励，佣金可以提现，也可以转换成余额在平台进行消费。</p><p>5、更多优惠活动请随时关注平台更新信息。</p>'
			]);

			Db::name('admin_set_sms')->insert(['aid'=>$aid]);

			Db::name('shop_group')->insert(['aid'=>$aid,'name'=>'最新']);
			Db::name('shop_group')->insert(['aid'=>$aid,'name'=>'热卖']);
			Db::name('shop_group')->insert(['aid'=>$aid,'name'=>'推荐']);
			Db::name('shop_group')->insert(['aid'=>$aid,'name'=>'促销']);

			$shopcid = Db::name('shop_category')->insertGetId(['aid'=>$aid,'name'=>'分类一','pic'=>PRE_URL.'/static/imgsrc/picture-1.jpg']);
			$shopcid = Db::name('shop_category')->insertGetId(['aid'=>$aid,'name'=>'分类二','pic'=>PRE_URL.'/static/imgsrc/picture-2.jpg']);
			$shopcid = Db::name('shop_category')->insertGetId(['aid'=>$aid,'name'=>'分类三','pic'=>PRE_URL.'/static/imgsrc/picture-3.jpg']);

			Db::name('shop_sysset')->insert(['aid'=>$aid]);
			Db::name('seckill_sysset')->insert(['aid'=>$aid]);
			Db::name('collage_sysset')->insert(['aid'=>$aid,'pics'=>PRE_URL.'/static/imgsrc/pintuan_banner1.png']);
			Db::name('kanjia_sysset')->insert(['aid'=>$aid,'pic'=>PRE_URL.'/static/imgsrc/kanjia_banner.png']);
			Db::name('scoreshop_sysset')->insert(['aid'=>$aid]);
			Db::name('business_sysset')->insert(['aid'=>$aid]);

			Db::name('signset')->insert([
				'aid'=>$aid,
				'score'=>1,
				'lxqdset'=>'[{"days":"3","score":"2"},{"days":"7","score":"3"},{"days":"15","score":"5"}]',
				'lxzsset'=>'[{"days":"3","score":"10"},{"days":"7","score":"20"},{"days":"10","score":"30"},{"days":"15","score":"40"}]',
				'guize'=>'<p>每天签到即可获得一个积分；</p><p>连续签到3天以上，每天签到获得2积分；</p><p>连续签到7天以上，每天签到获得3积分；</p><p>连续签到15天以上，每天签到获得5积分；</p><p>连续签到3天，额外赠送10积分；</p><p>连续签到7天，额外赠送20积分；</p><p>连续签到10天，额外赠送30积分；</p><p>连续签到15天，额外赠送40积分。</p>'
			]);

			$insertdata = [];
			$insertdata['aid'] = $aid;
			$insertdata['name'] = $sysset['name'] ? $sysset['name'] : '主页';
			$insertdata['ishome'] = 1;
			$insertdata['pageinfo'] = '[{"id":"M0000000000001","temp":"topbar","params":{"title":"'.$insertdata['name'].'","bgcolor":"#F6F6F6","quanxian":{"all":true},"fufei":"0","showgg":0,"guanggao":"'.PRE_URL.'/static/imgsrc/picture-1.jpg","hrefurl":"","ggrenqun":{"0":true},"cishu":"0"}}]';
			$insertdata['content'] = '[{"id":"M161781932574672351","temp":"search","params":{"placeholder":"输入关键字搜索您感兴趣的商品","color":"#666666","bgcolor":"#f5f5f5","borderradius":5,"bordercolor":"#FFFFFF","hrefurl":"/shopPackage/shop/search","hrefname":"基础功能>商品搜索","margin_x":"0","margin_y":"0","padding_x":"6","padding_y":"6","quanxian":{"all":true},"platform":{"all":true}},"data":"","other":"","content":""}, {"id":"M1617819327569344084","temp":"banner","params":{"shape":"","align":"center","bgcolor":"#ffffff","margin_x":"0","margin_y":"0","padding_x":"0","padding_y":"0","height":"200","indicatordots":"1","indicatorcolor":"#edeef0","indicatoractivecolor":"#3db51e","interval":5,"previous_margin":0,"next_margin":0,"quanxian":{"all":true},"platform":{"all":true}},"data":[{"id":"B0000000000001","imgurl":"'.PRE_URL.'/static/imgsrc/banner-1.jpg","hrefurl":""}, {"id":"B0000000000002","imgurl":"'.PRE_URL.'/static/imgsrc/banner-2.jpg","hrefurl":""}],"other":"","content":""}, {"id":"M1617819329073434298","temp":"notice","params":{"showimg":0,"img":"'.PRE_URL.'/static/imgsrc/hotdot3.png","showicon":1,"icon":"'.PRE_URL.'/static/imgsrc/notice2.png","color":"#666666","bgcolor":"#ffffff","scroll":3,"fontsize":"14","padding_x":"5","padding_y":"7","margin_x":"0","margin_y":"0","borderradius":0,"quanxian":{"all":true},"platform":{"all":true}},"data":[{"id":"N001","title":"这里是第一条自定义公告的标题","hrefurl":""}, {"id":"N002","title":"这里是第二条自定义公告的标题","hrefurl":""}],"other":"","content":""}, {"id":"M161781933705712200","temp":"product","params":{"style":"2","bgcolor":"#ffffff","showname":"1","showcart":"1","cartimg":"'.PRE_URL.'/static/imgsrc/cart.svg","showprice":"1","showsales":"1","saleimg":"","productfrom":"1","bid":"0","sortby":"sort","proshownum":6,"margin_x":"0","margin_y":"0","padding_x":"8","padding_y":"8","group":{"all":true},"quanxian":{"all":true},"platform":{"all":true}},"data":[],"other":"","content":""}]';
			$insertdata['createtime'] = time();
			Db::name('designerpage')->insert($insertdata);


			$insertdata = [];
			$insertdata['aid'] = $aid;
			$insertdata['name'] = '会员中心';
			$insertdata['ishome'] = 2;
			$insertdata['pageinfo'] = '[{"id":"M0000000000002","temp":"topbar","params":{"title":"会员中心","bgcolor":"#F6F6F6"}}]';
			$insertdata['content'] = '[{"id":"M1617821038824192432","temp":"userinfo","params":{"moneyshow":"1","scoreshow":"1","couponshow":"1","cardshow":"0","levelshow":"1","ordershow":"1","commissionshow":"1","bgimg":"'.PRE_URL.'/static/imgsrc/userinfobg.png","style":"2","margin_x":"0","margin_y":0,"padding_x":10,"padding_y":10,"quanxian":{"all":true},"platform":{"all":true}},"data":{},"other":"","content":""},{"id":"M161782071160920389","temp":"menu","params":{"num":"4","radius":"0","fontsize":"12","fontheight":"20","pernum":"10","bgcolor":"#ffffff","margin_x":"10","margin_y":0,"padding_x":"5","padding_y":"5","iconsize":"30","showicon":"1","showline":"0","showtitle":"1","title":"我的推广","titlesize":"14","titlecolor":"#333333","boxradius":"8","quanxian":{"'.$level2id.'":true,"all":false},"platform":{"all":true}},"data":[{"id":"F0000000000001","imgurl":"'.PRE_URL.'/static/imgsrc/ico-myteam.png","text":"我的团队","hrefurl":"/activity/commission/myteam","color":"#666666","hrefname":"基础功能>我的团队"},{"id":"F0000000000002","imgurl":"'.PRE_URL.'/static/imgsrc/ico-downorder.png","text":"分销订单","hrefurl":"/activity/commission/downorder","color":"#666666","hrefname":"基础功能>分销订单"},{"id":"F0000000000003","imgurl":"'.PRE_URL.'/static/imgsrc/ico-poster.png","text":"分享海报","hrefurl":"/activity/commission/poster","color":"#666666","hrefname":"基础功能>分享海报"},{"id":"F0000000000004","imgurl":"'.PRE_URL.'/static/imgsrc/ico-commission.png","text":"我的佣金","hrefurl":"/activity/commission/index","color":"#666666","hrefname":"基础功能>我的佣金"}],"other":"","content":""},{"id":"M1617821690792736493","temp":"blank","params":{"height":"10","bgcolor":"#f5f5f5","margin_x":"0","margin_y":"0","quanxian":{"'.$level2id.'":true,"all":false},"platform":{"all":true}},"data":"","other":"","content":""},{"id":"M1596398978642125977","temp":"menu","params":{"num":"4","radius":"0","fontsize":"12","fontheight":20,"pernum":"12","bgcolor":"#ffffff","margin_x":10,"margin_y":0,"padding_x":5,"padding_y":5,"iconsize":30,"showicon":"1","showline":"0","showtitle":"1","title":"常用工具","titlecolor":"#333333","boxradius":8,"titlesize":14,"platform":{"all":true},"quanxian":{"all":true}},"data":[{"id":"F0000000000001","imgurl":"'.PRE_URL.'/static/imgsrc/ico2-cart.png","text":"我的购物车","hrefurl":"/shopPackage/shop/cart","color":"#666666","hrefname":"基础功能>购物车"},{"id":"F0000000000002","imgurl":"'.PRE_URL.'/static/imgsrc/ico2-favrite.png","text":"我的收藏","hrefurl":"/pagesExa/my/favorite","color":"#666666","hrefname":"基础功能>我的收藏"},{"id":"F0000000000003","imgurl":"'.PRE_URL.'/static/imgsrc/ico2-zuji.png","text":"我的足迹","hrefurl":"/pagesExa/my/history","color":"#666666","hrefname":"基础功能>我的足迹"},{"id":"F0000000000004","imgurl":"'.PRE_URL.'/static/imgsrc/ico2-quan.png","text":"我的优惠券","hrefurl":"/pagesExb/coupon/mycoupon","color":"#666666","hrefname":"基础功能>我的优惠券"},{"id":"F0000000000005","imgurl":"'.PRE_URL.'/static/imgsrc/ico2-lingquan.png","text":"领券中心","hrefurl":"/pagesExb/coupon/couponlist","color":"#666666","hrefname":"基础功能>领券中心"},{"id":"M1596399075025131459","imgurl":"'.PRE_URL.'/static/imgsrc/ico2-tixian.png","text":"余额提现","hrefurl":"/pagesExb/money/withdraw","color":"#666666","hrefname":"基础功能>卡金提现"},{"id":"M159639907692086731","imgurl":"'.PRE_URL.'/static/imgsrc/ico2-mingxi.png","text":"余额明细","hrefurl":"/pagesExb/money/moneylog","color":"#666666","hrefname":"基础功能>余额明细"},{"id":"M1596399078152887395","imgurl":"'.PRE_URL.'/static/imgsrc/ico2-address.png","text":"收货地址","hrefurl":"/pages/address/address","color":"#666666","hrefname":"基础功能>收货地址"}],"other":"","content":""},{"id":"M1592344367534823433","temp":"blank","params":{"height":15,"bgcolor":"#f7f7f8","margin_x":"0","margin_y":"0","platform":{"all":true},"quanxian":{"all":true}},"data":"","other":"","content":""}]';
			$insertdata['createtime'] = time();
			Db::name('designerpage')->insert($insertdata);

			$data_index_mp = jsonEncode([
				'poster_bg' => PRE_URL.'/static/imgsrc/posterbg.jpg',
				'poster_data' =>[
					['left' => '30px','top' => '70px','type' => 'img','width' => '285px','height' => '285px','src' => PRE_URL.'/static/imgsrc/picture-1.jpg'],
					['left' => '30px','top' => '370px','type' => 'textarea','width' => '286px','height' => '47px','size' => '16px','color' => '#000','content' => '商城系统'],
					['left' => '34px','top' => '452px','type' => 'head','width' => '47px','height' => '47px','radius' => '100'],
					['left' => '89px','top' => '459px','type' => 'text','width' => '50px','height' => '18px','size' => '16px','color' => '#333333','content' => '[昵称]'],
					['left' => '90px','top' => '484px','type' => 'text','width' => '98px','height' => '14px','size' => '12px','color' => '#B6B6B6','content' => '推荐您加入'],
					['left' => '221px','top' =>'446px','type' => 'qrmp','width' => '94px','height' => '94px','size' => ''],
				]
			]);
			$data_index_wx = jsonEncode([
				'poster_bg' => PRE_URL.'/static/imgsrc/posterbg.jpg',
				'poster_data' =>[
					['left' => '30px','top' => '70px','type' => 'img','width' => '285px','height' => '285px','src' => PRE_URL.'/static/imgsrc/picture-1.jpg'],
					['left' => '30px','top' => '370px','type' => 'textarea','width' => '286px','height' => '47px','size' => '16px','color' => '#000','content' => '商城系统'],
					['left' => '34px','top' => '452px','type' => 'head','width' => '47px','height' => '47px','radius' => '100'],
					['left' => '89px','top' => '459px','type' => 'text','width' => '50px','height' => '18px','size' => '16px','color' => '#333333','content' => '[昵称]'],
					['left' => '90px','top' => '484px','type' => 'text','width' => '98px','height' => '14px','size' => '12px','color' => '#B6B6B6','content' => '推荐您加入'],
					['left' => '221px','top' =>'446px','type' => 'qrwx','width' => '94px','height' => '94px','size' => ''],
				]
			]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'index','platform'=>'mp','content'=>$data_index_mp,'guize'=>"第一步、转发链接或图片给微信好友；\r\n第二步、从您转发的链接或图片进入商城的好友，系统将自动锁定成为您的客户, 他们在商城中购买商品，您就可以获得佣金；\r\n第三步、您可以在会员中心查看【我的团队】和【分销订单】，好友确认收货后佣金方可提现。"]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'index','platform'=>'wx','content'=>$data_index_wx,'guize'=>"第一步、转发链接或图片给微信好友；\r\n第二步、从您转发的链接或图片进入商城的好友，系统将自动锁定成为您的客户, 他们在商城中购买商品，您就可以获得佣金；\r\n第三步、您可以在会员中心查看【我的团队】和【分销订单】，好友确认收货后佣金方可提现。"]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'index','platform'=>'alipay','content'=>$data_index_mp,'guize'=>"第一步、转发链接或图片给微信好友；\r\n第二步、从您转发的链接或图片进入商城的好友，系统将自动锁定成为您的客户, 他们在商城中购买商品，您就可以获得佣金；\r\n第三步、您可以在会员中心查看【我的团队】和【分销订单】，好友确认收货后佣金方可提现。"]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'index','platform'=>'baidu','content'=>$data_index_mp,'guize'=>"第一步、转发链接或图片给微信好友；\r\n第二步、从您转发的链接或图片进入商城的好友，系统将自动锁定成为您的客户, 他们在商城中购买商品，您就可以获得佣金；\r\n第三步、您可以在会员中心查看【我的团队】和【分销订单】，好友确认收货后佣金方可提现。"]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'index','platform'=>'toutiao','content'=>$data_index_mp,'guize'=>"第一步、转发链接或图片给微信好友；\r\n第二步、从您转发的链接或图片进入商城的好友，系统将自动锁定成为您的客户, 他们在商城中购买商品，您就可以获得佣金；\r\n第三步、您可以在会员中心查看【我的团队】和【分销订单】，好友确认收货后佣金方可提现。"]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'index','platform'=>'qq','content'=>$data_index_mp,'guize'=>"第一步、转发链接或图片给微信好友；\r\n第二步、从您转发的链接或图片进入商城的好友，系统将自动锁定成为您的客户, 他们在商城中购买商品，您就可以获得佣金；\r\n第三步、您可以在会员中心查看【我的团队】和【分销订单】，好友确认收货后佣金方可提现。"]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'index','platform'=>'h5','content'=>$data_index_mp,'guize'=>"第一步、转发链接或图片给微信好友；\r\n第二步、从您转发的链接或图片进入商城的好友，系统将自动锁定成为您的客户, 他们在商城中购买商品，您就可以获得佣金；\r\n第三步、您可以在会员中心查看【我的团队】和【分销订单】，好友确认收货后佣金方可提现。"]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'index','platform'=>'app','content'=>$data_index_mp,'guize'=>"第一步、转发链接或图片给微信好友；\r\n第二步、从您转发的链接或图片进入商城的好友，系统将自动锁定成为您的客户, 他们在商城中购买商品，您就可以获得佣金；\r\n第三步、您可以在会员中心查看【我的团队】和【分销订单】，好友确认收货后佣金方可提现。"]);


			$data_product_mp = jsonEncode([
				'poster_bg' => PRE_URL.'/static/imgsrc/posterbg.jpg',
				'poster_data' => [
					['left' => '30px','top' => '70px','type' => 'pro_img','width' => '285px','height' => '285px'],
					['left' => '30px','top' => '370px','type' => 'textarea','width' => '286px','height' => '47px','size' => '16px','color' => '#000','content' => '[商品名称]'],
					['left' => '34px','top' => '452px','type' => 'head','width' => '47px','height' => '47px','radius' => '100'],
					['left' => '89px','top' => '459px','type' => 'text','width' => '50px','height' => '18px','size' => '16px','color' => '#333333','content' => '[昵称]'],
					['left' => '90px','top' => '484px','type' => 'text','width' => '98px','height' => '14px','size' => '12px','color' => '#B6B6B6','content' => '推荐给你一个宝贝'],
					['left' => '35px','top' => '516px','type' => 'text','width' => '142px','height' => '22px','size' => '20px','color' => '#FD0000','content' => '￥[商品销售价]'],
					['left' => '125px','top' => '518px','type' => 'text','width' => '135px','height' => '16px','size' => '14px','color' => '#BBBBBB','content' => '原价:￥[商品市场价]'],
					['left' => '221px','top' => '446px','type' => 'qrmp','width' => '94px','height' => '94px','size' => '',],
				]
			]);
			$data_product_wx = jsonEncode([
				'poster_bg' => PRE_URL.'/static/imgsrc/posterbg.jpg',
				'poster_data' => [
					['left' => '30px','top' => '70px','type' => 'pro_img','width' => '285px','height' => '285px'],
					['left' => '30px','top' => '370px','type' => 'textarea','width' => '286px','height' => '47px','size' => '16px','color' => '#000','content' => '[商品名称]'],
					['left' => '34px','top' => '452px','type' => 'head','width' => '47px','height' => '47px','radius' => '100'],
					['left' => '89px','top' => '459px','type' => 'text','width' => '50px','height' => '18px','size' => '16px','color' => '#333333','content' => '[昵称]'],
					['left' => '90px','top' => '484px','type' => 'text','width' => '98px','height' => '14px','size' => '12px','color' => '#B6B6B6','content' => '推荐给你一个宝贝'],
					['left' => '35px','top' => '516px','type' => 'text','width' => '142px','height' => '22px','size' => '20px','color' => '#FD0000','content' => '￥[商品销售价]'],
					['left' => '125px','top' => '518px','type' => 'text','width' => '135px','height' => '16px','size' => '14px','color' => '#BBBBBB','content' => '原价:￥[商品市场价]'],
					['left' => '221px','top' => '446px','type' => 'qrwx','width' => '94px','height' => '94px','size' => '',],
				]
			]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'product','platform'=>'mp','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'product','platform'=>'wx','content'=>$data_product_wx]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'product','platform'=>'alipay','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'product','platform'=>'baidu','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'product','platform'=>'toutiao','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'product','platform'=>'qq','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'product','platform'=>'h5','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'product','platform'=>'app','content'=>$data_product_mp]);

			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collage','platform'=>'mp','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collage','platform'=>'wx','content'=>$data_product_wx]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collage','platform'=>'alipay','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collage','platform'=>'baidu','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collage','platform'=>'toutiao','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collage','platform'=>'qq','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collage','platform'=>'h5','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collage','platform'=>'app','content'=>$data_product_mp]);

			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collageteam','platform'=>'mp','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collageteam','platform'=>'wx','content'=>$data_product_wx]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collageteam','platform'=>'alipay','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collageteam','platform'=>'baidu','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collageteam','platform'=>'toutiao','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collageteam','platform'=>'qq','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collageteam','platform'=>'h5','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'collageteam','platform'=>'app','content'=>$data_product_mp]);

			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjia','platform'=>'mp','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjia','platform'=>'wx','content'=>$data_product_wx]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjia','platform'=>'alipay','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjia','platform'=>'baidu','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjia','platform'=>'toutiao','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjia','platform'=>'qq','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjia','platform'=>'h5','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjia','platform'=>'app','content'=>$data_product_mp]);

			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjiajoin','platform'=>'mp','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjiajoin','platform'=>'wx','content'=>$data_product_wx]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjiajoin','platform'=>'alipay','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjiajoin','platform'=>'baidu','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjiajoin','platform'=>'toutiao','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjiajoin','platform'=>'qq','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjiajoin','platform'=>'h5','content'=>$data_product_mp]);
			Db::name('admin_set_poster')->insert(['aid'=>$aid,'type'=>'kanjiajoin','platform'=>'app','content'=>$data_product_mp]);

			$insertdata = [];
			$insertdata['aid'] = $aid;
			$insertdata['menucount'] = 4;
			$insertdata['indexurl'] = '/pages/index/index';
			$insertdata['menudata'] = jsonEncode([
				"color"=>"#BBBBBB",
				"selectedColor"=>"#FD4A46",
				"backgroundColor"=>"#ffffff",
				"borderStyle"=>"black",
				"position"=>"bottom",
				"list"=>[
					["text"=>"首页","pagePath"=>"/pages/index/index","iconPath"=>PRE_URL.'/static/img/tabbar/home.png',"selectedIconPath"=>PRE_URL.'/static/img/tabbar/home2.png',"pagePathname"=>"基础功能>首页"
					],
					["text"=>"分类","pagePath"=>"/pagesExb/shop/classify","iconPath"=>PRE_URL.'/static/img/tabbar/category.png',"selectedIconPath"=>PRE_URL.'/static/img/tabbar/category2.png',"pagePathname"=>"基础功能>分类商品"
					],
					["text"=>"购物车","pagePath"=>"/shopPackage/shop/cart","iconPath"=>PRE_URL.'/static/img/tabbar/cart.png',"selectedIconPath"=>PRE_URL.'/static/img/tabbar/cart2.png',"pagePathname"=>"基础功能>购物车"
					],
					["text"=>"我的","pagePath"=>"/pages/my/usercenter","iconPath"=>PRE_URL.'/static/img/tabbar/my.png',"selectedIconPath"=>PRE_URL.'/static/img/tabbar/my2.png',"pagePathname"=>"基础功能>会员中心"
					],
					["text"=>"导航名称","pagePath"=>"","iconPath"=>PRE_URL.'/static/img/tabbar/category.png',"selectedIconPath"=>PRE_URL.'/static/img/tabbar/category2.png',"pagePathname"=>""
					],
				]
			]);
			$insertdata['navigationBarBackgroundColor'] = '#333333';
			$insertdata['navigationBarTextStyle'] = 'white';
			$insertdata['platform'] = 'mp';
			Db::name('designer_menu')->insert($insertdata);
			$insertdata['platform'] = 'wx';
			Db::name('designer_menu')->insert($insertdata);
			$insertdata['platform'] = 'alipay';
			Db::name('designer_menu')->insert($insertdata);
			$insertdata['platform'] = 'baidu';
			Db::name('designer_menu')->insert($insertdata);
			$insertdata['platform'] = 'toutiao';
			Db::name('designer_menu')->insert($insertdata);
			$insertdata['platform'] = 'qq';
			Db::name('designer_menu')->insert($insertdata);
			$insertdata['platform'] = 'h5';
			Db::name('designer_menu')->insert($insertdata);
			$insertdata['platform'] = 'app';
			Db::name('designer_menu')->insert($insertdata);

			$html = file_get_contents(ROOT_PATH.'/h5/index.html');
			$thishtml = str_replace('var uniacid=1;','var uniacid='.$aid.';',$html);
			file_put_contents(ROOT_PATH.'h5/'.$aid.'.html',$thishtml);
		}
	}

	//设计页面数据处理
	public static function initpagecontent($pagecontent,$aid,$mid=-1,$platform='all',$latitude='',$longitude='',$area=''){
		$pagecontent = json_decode($pagecontent,true);
		if($platform !='all'){
			$newpagecontent = [];
			foreach($pagecontent as $k=>$v){
				if($v['params']['platform']['all'] || $v['params']['platform'][$platform]){
					$newpagecontent[] = $v;
				}
			}
			$pagecontent = $newpagecontent;
		}
		if($mid !='-1'){
			if($mid == 0){
				$levelid = Db::name('member_level')->where('aid',aid)->where('isdefault',1)->find();
			}else{
				$levelid = Db::name('member')->where('aid',aid)->where('id',$mid)->value('levelid');
			}
			$newpagecontent = [];
			foreach($pagecontent as $k=>$v){
				if($v['params']['quanxian']['all'] || $v['params']['quanxian'][$levelid] || ($v['params']['showmids'] && in_array($mid,explode(',',$v['params']['showmids'])))){
					$newpagecontent[] = $v;
				}
			}
			$pagecontent = $newpagecontent;
		}

        if($mid > 0){
            $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
        }else{
            $member = [];
        }
		foreach($pagecontent as $k=>$v){
			if($v['temp'] == 'text'){ //文本
				$showcontent = $v['params']['content'];
				if(strpos($showcontent,'[会员数]')!==false){
					$defaultlv = Db::name('member_level')->where('aid',aid)->where('isdefault',1)->find();
					$membercount = Db::name('member')->where('aid',$aid)->where('levelid','<>',$defaultlv['id'])->count();
					$showcontent = str_replace('[会员数]',$membercount,$showcontent);
				}elseif(strpos($showcontent,'[会员数+')!==false){
					$defaultlv = Db::name('member_level')->where('aid',aid)->where('isdefault',1)->find();
					$membercount = Db::name('member')->where('aid',$aid)->where('levelid','<>',$defaultlv['id'])->count();
					$showcontent = preg_replace_callback('/\[会员数\+(\d+)\]/',function($matches) use ($membercount){return $membercount + $matches[1];},$showcontent);
				}
				$pagecontent[$k]['params']['showcontent'] = $showcontent;
			}
			if($v['temp'] == 'cube'){ //图片魔方 获取魔方高度
				$maxheight = 0;
				foreach($v['params']['layout'] as $k1=>$rows){
					foreach($rows as $k2=>$col){
						if(!$col['isempty'] && $k1 + $col['rows'] > $maxheight){
							$maxheight = $k1 + $col['rows'];
						}
					}
				}
				$pagecontent[$k]['params']['maxheight'] = $maxheight;
			}elseif($v['temp'] == 'product'){//产品列表 获取产品信息
				// 检查是否启用了混合模式
				$isMixedMode = isset($v['params']['mixedMode']) && $v['params']['mixedMode'] === true;
				
				if($v['params']['showbname'] == '1'){
					$bArr = Db::name('business')->where('aid',$aid)->column('id,name,logo','id');
					$sysset = Db::name('admin_set')->where('aid',$aid)->field('name,logo')->find();
					$bArr['0'] = ['id'=>0,'name'=>$sysset['name'],'logo'=>$sysset['logo']];
				}
				if($v['params']['productfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $pro){
						$field = 'id proid,bid,name,pic,sell_price,lvprice,lvprice_data,market_price,sales,sort,price_type,stock,cid,scoredkmaxsethuang,scoredkmaxunithuang,show_price,hide_price_text,is_newcustom,yh_price';
				        $newpro = Db::name('shop_product')->field($field)->where('aid',$aid)->where('id',$pro['proid'])->where('status',1)->where('ischecked',1)->find();
						if($newpro){
							$newpro['id'] = $pro['id'];
							// 添加 is_member_yh 字段
							$newpro['is_member_yh'] = isset($member['is_yh']) ? $member['is_yh'] : 0;
							
							if($newpro['lvprice']==1 && $member){
								$lvprice_data = json_decode($newpro['lvprice_data'],true);
								if($lvprice_data && isset($lvprice_data[$member['levelid']])){
									$newpro['sell_price'] = $lvprice_data[$member['levelid']];
								}
							}
							if($v['params']['showbname'] == '1'){
								$newpro['binfo'] = $bArr[$newpro['bid']];
							}
							if($v['params']['showcoupon']){
								$couponlist = Db::name('coupon')->where('aid',$aid)->where('bid',$newpro['bid'])->where('isgive','<>',2)->where('tolist',1)->where('type','in','1')->where("unix_timestamp(starttime)<=".time()." and unix_timestamp(endtime)>=".time())->order('sort desc,id desc')->select()->toArray();
								$newcplist = [];
								foreach($couponlist as $k3=>$v3){
									$showtj = explode(',',$v3['showtj']);
									if(!in_array('-1',$showtj) && !in_array($member['levelid'],$showtj)){ //不是所有人
										continue;
									}
									//0全场通用,1指定类目,2指定商品
									if(!in_array($v3['fwtype'],[0,1,2])){
										continue;
									}
									if($v3['fwtype']==2){//指定商品可用
										$productids = explode(',',$v3['productids']);
										if(!in_array($newpro['proid'],$productids)){
											continue;
										}
									}
									if($v3['fwtype']==1){//指定类目可用
										$categoryids = explode(',',$v3['categoryids']);
										$cids = explode(',',$newpro['cid']);
										$clist = Db::name('shop_category')->where('pid','in',$categoryids)->select()->toArray();
										foreach($clist as $kc=>$vc){
											$categoryids[] = $vc['id'];
											$cate2 = Db::name('shop_category')->where('pid',$vc['id'])->find();
											$categoryids[] = $cate2['id'];
										}
										if(!array_intersect($cids,$categoryids)){
											continue;
										}
									}
									$newcplist[] = $v3;
								}
								$newpro['couponlist'] = $newcplist;
							}

							// 2024-07-09 - 隐藏价格
							if($newpro['show_price'] > 0) {
								//限制等级
								$levelids = explode(',', $newpro['show_price']);
								if(!in_array($member['levelid'], $levelids)) {
									$newpro['sell_price'] = $newpro['hide_price_text'];
									unset($newpro['market_price']);
								}
							}

							// 计算红包 - 需要传入原始的商品数据
							$newpro['huang_dx'] = \app\common\Member::getShopProductScoredkmaxsethuang(aid, [
								'scoredkmaxsethuang' => $newpro['scoredkmaxsethuang'],
								'scoredkmaxunithuang' => $newpro['scoredkmaxunithuang']
							]);

							$newdata[] = $newpro;
						}
					}
					$pagecontent[$k]['data'] = $newdata;
				}else{
				   
					$where = [];
					$where[] = ['aid','=',$aid];
					$where[] = ['ischecked','=',1];
					//$where[] = ['status','=',1];
					if(defined('isdouyin') && isdouyin == 1){
						$where[] = ['douyin_product_id','<>',''];
					}else{
						$where[] = ['douyin_product_id','=',''];
					}
					$nowtime = time();
					$nowhm = date('H:i');
					$where[] = Db::raw("`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime) or (`status`=3 and ((start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm'))) )");

					if($v['params']['category']){
                        $where2 = "find_in_set('-1',showtj)";
                        if($member){
                            $where2 .= " or find_in_set('".$member['levelid']."',showtj)";
                            if($member['subscribe']==1){
                                $where2 .= " or find_in_set('0',showtj)";
                            }
                        }
                        $tjwhere[] = Db::raw($where2);
						$cid = intval($v['params']['category']);
						$chidlc = Db::name('shop_category')->where($tjwhere)->where('aid',$aid)->where('pid',$cid)->select()->toArray();
                        if($chidlc){
                            $cids = array($cid);
                            $whereCid = '(';
                            $whereCid .= " find_in_set({$cid},cid) or ";
                            foreach($chidlc as $k2 => $c){
                                if(count($chidlc) == ($k2 + 1))
                                    $whereCid .= "find_in_set({$c['id']},cid)";
                                else
                                    $whereCid .= " find_in_set({$c['id']},cid) or ";
                            }
                            $where[] = Db::raw($whereCid . ')');
                        }else{
                            $where[] = Db::raw("find_in_set({$cid},cid)");
                        }
					}else{
                        }
					if($v['params']['category2']){
						$cid2 = intval($v['params']['category2']);
						if($cid2 > 0){
							$chidlc2 = Db::name('shop_category2')->where('aid',$aid)->where('pid',$cid2)->column('id');
							if($chidlc2){
								$chidlc2 = array_merge($chidlc2, [$cid2]);
								$whereCid2 = '(';
								foreach($chidlc2 as $k => $c){
									if(count($chidlc2) == ($k + 1))
										$whereCid2 .= "find_in_set({$c},cid2)";
									else
										$whereCid2 .= " find_in_set({$c},cid2) or ";
								}
								$where[] = Db::raw($whereCid2 . ')');
							}else{
								$where[] = Db::raw("find_in_set({$cid2},cid2)");
							}
						}
					}
					if($v['params']['group']){
						$_string = array();
						foreach($v['params']['group'] as $gid=>$istrue){
							$gid = strval($gid);
							if($istrue=='true'){
								if($gid == 'all'){
									$_string[] = "1=1";
								}elseif($gid == '0'){
									$_string[] = "gid is null or gid=''";
								}else{
									$_string[] = "find_in_set({$gid},gid)";
								}
							}
						}
						if(!$_string){
							$where2 = '0=1';
						}else{
							$where2 = implode(" or ",$_string);
						}
					}else{
						$where2 = '1=1';
					}
					if($v['params']['bid']!=='' && $v['params']['bid']!==null){
						$where[] = ['bid','=',$v['params']['bid']];
					}
					$where3 = "find_in_set('-1',showtj)";
					if($member){
						$where3.= " or find_in_set('".$member['levelid']."',showtj)";
						if($member['subscribe']==1){
							$where3 .= " or find_in_set('0',showtj)";
						}
					}

					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'createtime') $order = 'createtime';
					if($v['params']['sortby'] == 'sales') $order = 'sales desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');

					$bjuli = [];
					if($v['params']['sortby'] == 'juli' && $latitude && $longitude){
						$border = Db::raw("({$longitude}-longitude)*({$longitude}-longitude) + ({$latitude}-latitude)*({$latitude}-latitude) ");
						$blist = Db::name('business')->where('aid',$aid)->where('status',1)->where("longitude!='' and latitude!=''")->field('id,longitude,latitude')->order($border)->select()->toArray();
						$bids = [];
						$sysset = Db::name('admin_set')->where('aid',$aid)->find();
						$b0juli = getdistance($longitude,$latitude,$sysset['longitude'],$sysset['latitude'],2);
						foreach($blist as $binfo){
							$juli = getdistance($longitude,$latitude,$binfo['longitude'],$binfo['latitude'],2);
							if($juli > $b0juli && !in_array('0',$bids)){
								$bids[] = '0';
								$bjuli['0'] = ''.$b0juli.'km';
							}
							$bids[] = $binfo['id'];
							$bjuli[''.$binfo['id']] = ''.$juli.'km';
						}
						$order = Db::raw('field(bid,'.implode(',',$bids).'),sort desc,id desc');
					}
					
					//区域限制
        			$area_set = Db::name('admin_set')->where('aid',aid)->field('area_on,areamode')->find();
        			$area_id = input('param.area_id/d');
        			if($area_set && $area_set['area_on'] && $area_id){
        			    $permission = Db::name('admin_set_area_permission')->where(['a_area'=>md5(aid.$area_id)])->value('permission');
        			    //var_dump($permission);
        			    if($permission){
            			    $permission = json_decode($permission,true);
            			    if($area_set['areamode']==0)$limit_field = 'province_id';
            			    if($area_set['areamode']==1)$limit_field = 'city_id';
            			    if($area_set['areamode']==2)$limit_field = 'district_id';
            			    if($permission['product_visible_area']==0){
            			        //仅可见该区域
            			        $where[] = [$limit_field,'=',$area_id];
            			    }else if($permission['product_visible_area']==1){
            			        //可见部分区域
            			        $where[] = [$limit_field,'in',explode(',',$permission['product_visible_areas'])];
            			    }else if($permission['product_visible_area']==2){
            			        //不可见部分区域
            			        $where[] = [$limit_field,'notin',explode(',',$permission['product_unvisible_areas'])];
            			    } 
        			    }
        			}
					

					$field = 'id proid,bid,name,pic,sell_price,lvprice,lvprice_data,market_price,sales,sort,price_type,stock,cid,scoredkmaxsethuang,scoredkmaxunithuang,show_price,hide_price_text,is_newcustom,yh_price';
			        $result = Db::name('shop_product')->field($field)->where($where)->where($where2)->where($where3)->order($order)->limit($v['params']['proshownum'])->select()->toArray();
//                    dd(Db::getlastsql());
					if(!$result) $result = array();
					foreach($result as $k2=>$v2){
					   // var_dump($member['is_yh']);
					   // $result[$k2]['is_member_yh'] = $member['is_yh']?$member['is_yh']:1;
					   $result[$k2]['is_member_yh'] = isset($member['is_yh']) ? $member['is_yh'] : 0;
						$result[$k2]['id'] = 'G'.time().rand(10000000,99999999);
						if($v2['lvprice']==1 && $member){
							$lvprice_data = json_decode($v2['lvprice_data'],true);
							if($lvprice_data && isset($lvprice_data[$member['levelid']])){
								$result[$k2]['sell_price'] = $lvprice_data[$member['levelid']];
							}
						}
						if($bjuli){
							$result[$k2]['juli'] = $bjuli[''.$v2['bid']];
						}
						if($v['params']['showbname'] == '1'){
							$result[$k2]['binfo'] = $bArr[$v2['bid']];
						}
						if($v['params']['showcoupon']){
							$couponlist = Db::name('coupon')->where('aid',$aid)->where('bid',$v2['bid'])->where('isgive','<>',2)->where('tolist',1)->where('type','in','1,4,10')->where("unix_timestamp(starttime)<=".time()." and unix_timestamp(endtime)>=".time())->order('sort desc,id desc')->select()->toArray();
							$newcplist = [];
							foreach($couponlist as $k3=>$v3){
								$showtj = explode(',',$v3['showtj']);
								if(!in_array('-1',$showtj) && !in_array($this->member['levelid'],$showtj)){ //不是所有人
									continue;
								}
								//0全场通用,1指定类目,2指定商品
								if(!in_array($v3['fwtype'],[0,1,2])){
									continue;
								}
								if($v3['fwtype']==2){//指定商品可用
									$productids = explode(',',$v3['productids']);
									if(!in_array($v2['proid'],$productids)){
										continue;
									}
								}
								if($v3['fwtype']==1){//指定类目可用
									$categoryids = explode(',',$v3['categoryids']);
									$cids = explode(',',$v2['cid']);
									$clist = Db::name('shop_category')->where('pid','in',$categoryids)->select()->toArray();
									foreach($clist as $kc=>$vc){
										$categoryids[] = $vc['id'];
										$cate2 = Db::name('shop_category')->where('pid',$vc['id'])->find();
										$categoryids[] = $cate2['id'];
									}
									if(!array_intersect($cids,$categoryids)){
										continue;
									}
								}
								$newcplist[] = $v3;
							}
							$result[$k2]['couponlist'] = $newcplist;
						}
						
						//计算红包
			            $result[$k2]['huang_dx']=\app\common\Member::getShopProductScoredkmaxsethuang(aid,$v2);

			            // 2024-07-09 - 隐藏价格
			            if($result[$k2]['show_price'] > 0) {
			                //限制等级
			                $levelids = explode(',', $result[$k2]['show_price']);
			                if(!in_array($member['levelid'], $levelids)) {
			                	$result[$k2]['sell_price'] = $result[$k2]['hide_price_text'];
			                	unset($result[$k2]['market_price']);
			                }
			            }
					}
					
					 
					$pagecontent[$k]['data'] = $result;
				}
				
				// 检查是否启用了混合模式
				$isMixedMode = isset($v['params']['mixedMode']) && $v['params']['mixedMode'] === true;
				
				// 如果启用了混合模式，在后端处理文章和视频数据
				if ($isMixedMode) {
					// 记录日志
					\think\facade\Log::write("2024-07-23 15:30:00-INFO-[System.php][initpagecontent_001] 后端处理混合模式数据", 'info');
					
					// 初始化混合数据数组
					$pagecontent[$k]['mixed_data'] = [
						'products' => $pagecontent[$k]['data'],
						'articles' => [],
						'videos' => []
					];
					
					// 处理文章数据 (如果启用)
					if (isset($v['params']['mixArticle']) && $v['params']['mixArticle'] === true) {
						\think\facade\Log::write("2024-07-23 15:30:00-INFO-[System.php][initpagecontent_002] 处理混合模式文章数据", 'info');
						$articles = [];
						
						if (isset($v['params']['articleFrom']) && $v['params']['articleFrom'] == 1 && isset($v['params']['articleCategory'])) {
							// 从分类获取文章
							$category = intval($v['params']['articleCategory']);
							$shownum = isset($v['params']['articleShownum']) ? intval($v['params']['articleShownum']) : 5;
							
							$where = [
								['aid', '=', $aid],
								['status', '=', 1]
							];
							
							if ($category) {
								$chidlc = Db::name('article_category')->where('aid', $aid)->where('pid', $category)->select()->toArray();
								if ($chidlc) {
									$cids = [$category];
									foreach ($chidlc as $c) {
										$cids[] = intval($c['id']);
									}
									$where[] = ['cid', 'in', $cids];
								} else {
									$where[] = ['cid', '=', $category];
								}
							}
							
							$order = 'sort desc';
							if (isset($v['params']['articleSortby'])) {
								if ($v['params']['articleSortby'] == 'createtimedesc') $order = 'createtime desc';
								if ($v['params']['articleSortby'] == 'createtime') $order = 'createtime';
								if ($v['params']['articleSortby'] == 'readnum') $order = 'readnum desc';
							}
							
							$articles = Db::name('article')
								->field('id,name,cid,pic,content,description,readnum,createtime')
								->where($where)
								->order($order)
								->limit($shownum)
								->select()
								->toArray();
								
							// 标记数据类型为文章
							foreach ($articles as &$art) {
								$art['type'] = 'article';
								$art['id'] = 'A' . time() . rand(10000000, 99999999);
							}
						}
						
						$pagecontent[$k]['mixed_data']['articles'] = $articles;
					}
					
					// 处理短视频数据 (如果启用)
					if (isset($v['params']['mixVideo']) && $v['params']['mixVideo'] === true) {
						\think\facade\Log::write("2024-07-23 15:30:00-INFO-[System.php][initpagecontent_003] 处理混合模式视频数据", 'info');
						$videos = [];
						
						if (isset($v['params']['videoFrom']) && $v['params']['videoFrom'] == 1 && isset($v['params']['videoCategory'])) {
							// 从分类获取视频
							$category = intval($v['params']['videoCategory']);
							$shownum = isset($v['params']['videoShownum']) ? intval($v['params']['videoShownum']) : 3;
							
							$where = [
								['aid', '=', $aid],
								['status', '=', 1]
							];
							
							if ($category) {
								$where[] = ['cid', '=', $category];
							}
							
							$order = 'sort desc';
							if (isset($v['params']['videoSortby'])) {
								if ($v['params']['videoSortby'] == 'createtimedesc') $order = 'createtime desc';
								if ($v['params']['videoSortby'] == 'createtime') $order = 'createtime';
								if ($v['params']['videoSortby'] == 'view_num') $order = 'view_num desc';
							}
							
							$videos = Db::name('shortvideo')
								->field('id videoId,name,description,coverimg,view_num,zan_num,createtime')
								->where($where)
								->order($order)
								->limit($shownum)
								->select()
								->toArray();
								
							// 标记数据类型为视频
							foreach ($videos as &$vid) {
								$vid['type'] = 'video';
								$vid['id'] = 'V' . time() . rand(10000000, 99999999);
							}
						}
						
						$pagecontent[$k]['mixed_data']['videos'] = $videos;
					}
					
					// 记录日志
					\think\facade\Log::write("2024-07-23 15:30:00-INFO-[System.php][initpagecontent_004] 混合模式数据处理完成", 'info');
				}
            }elseif($v['temp'] == 'restaurant_product'){//菜品列表 获取菜品信息
                if($v['params']['productfrom'] == 0){//手动选择
                    $newdata = array();
                    foreach($v['data'] as $pro){
                        $newpro = Db::name('restaurant_product')->field('id proid,name,pic,market_price,sell_price,lvprice,lvprice_data,sales')->where('aid',$aid)->where('id',$pro['proid'])->where('status',1)->where('ischecked',1)->find();
                        if($newpro){
                            $newpro['id'] = $pro['id'];
                            if($newpro['lvprice']==1 && $member){
                                $lvprice_data = json_decode($newpro['lvprice_data'],true);
                                if($lvprice_data && isset($lvprice_data[$member['levelid']])){
                                    $newpro['sell_price'] = $lvprice_data[$member['levelid']];
                                }
                            }
                            $newdata[] = $newpro;
                        }
                    }
                    $pagecontent[$k]['data'] = $newdata;
                }else{
                    $where = [];
                    $where[] = ['aid','=',$aid];
                    $where[] = ['ischecked','=',1];
                    //$where[] = ['status','=',1];
                    $nowtime = time();
                    $nowhm = date('H:i');
                    $where[] = Db::raw("`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime) or (`status`=3 and ((start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm'))) )");

                    if($v['params']['category']){
                        $cid = intval($v['params']['category']);
                        $where[] = Db::raw("find_in_set({$cid},cid)");
                    }
                    $where2 = '1=1';
                    if($v['params']['bid']!=='' && $v['params']['bid']!==null){
                        $where[] = ['bid','=',$v['params']['bid']];
                    }

                    $order = 'sort desc';
                    if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
                    if($v['params']['sortby'] == 'createtimedesc') $order = 'create_time desc';
                    if($v['params']['sortby'] == 'createtime') $order = 'create_time';
                    if($v['params']['sortby'] == 'sales') $order = 'sales desc,sort desc';
                    if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
                    $result = Db::name('restaurant_product')->field('id proid,name,pic,sell_price,lvprice,lvprice_data,market_price,sales')->where($where)->where($where2)->order($order)->limit($v['params']['proshownum'])->select()->toArray();
                    if(!$result) $result = array();
                    foreach($result as $k2=>$v2){
                        $result[$k2]['id'] = 'G'.time().rand(10000000,99999999);
                        if($v2['lvprice']==1 && $member){
                            $lvprice_data = json_decode($v2['lvprice_data'],true);
                            if($lvprice_data && isset($lvprice_data[$member['levelid']])){
                                $result[$k2]['sell_price'] = $lvprice_data[$member['levelid']];
                            }
                        }
                    }
                    $pagecontent[$k]['data'] = $result;
                }
			}elseif($v['temp'] == 'scoreshop'){//产品列表 获取产品信息
				if($v['params']['productfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $pro){
						$field = 'id proid,name,pic,sell_price,score_price,money_price,sales,lvprice,lvprice_data';
				        $newpro = Db::name('scoreshop_product')->field($field)->where('aid',$aid)->where('id',$pro['proid'])->where('status',1)->find();
						if($newpro){
							$newpro['id'] = $pro['id'];
							if($newpro['lvprice']==1 && $member){
								$lvprice_data = json_decode($newpro['lvprice_data'],true);
								if($lvprice_data && isset($lvprice_data[$member['levelid']])){
									if(isset($lvprice_data[$member['levelid']]['money_price']))
									$newpro['money_price'] = $lvprice_data[$member['levelid']]['money_price'];
									if(isset($lvprice_data[$member['levelid']]['score_price']))
									$newpro['score_price'] = $lvprice_data[$member['levelid']]['score_price'];
								}
							}
							$newdata[] = $newpro;
						}
					}
					$pagecontent[$k]['data'] = $newdata;
				}else{
					$where = [];
					$where[] = ['aid','=',$aid];
					$where[] = ['status','=',1];
					if($v['params']['category']){
						$cid = intval($v['params']['category']);
						$chidlc = Db::name('scoreshop_category')->where('aid',$aid)->where('pid',$cid)->select()->toArray();
						if($chidlc){
							$cids = array($cid);
							foreach($chidlc as $c){
								$cids[] = intval($c['id']);
							}
							$where[] = ['cid','in',$cids];
						}else{
							$where[] = ['cid','=',$cid];
						}
					}
					if($v['params']['group']){
						$_string = array();
						foreach($v['params']['group'] as $gid=>$istrue){
							if($istrue=='true'){
								if($gid == '0'){
									$_string[] = "gid is null or gid=''";
								}else{
									$_string[] = "find_in_set({$gid},gid)";
								}
							}
						}
						if(!$_string){
							$where2 = '0=1';
						}else{
							$where2 = implode(" or ",$_string);
						}
					}else{
						$where2 = '1=1';
					}
					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'createtime') $order = 'createtime';
					if($v['params']['sortby'] == 'sales') $order = 'sales desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
					$field = 'id proid,name,pic,sell_price,score_price,money_price,sales,lvprice,lvprice_data';
			        $result = Db::name('scoreshop_product')->field($field)->where($where)->where($where2)->order($order)->limit($v['params']['proshownum'])->select()->toArray();
					if(!$result) $result = array();
					foreach($result as $k2=>$v2){
						$result[$k2]['id'] = 'G'.time().rand(10000000,99999999);

						if($v2['lvprice']==1 && $member){
							$lvprice_data = json_decode($v2['lvprice_data'],true);
							if($lvprice_data && isset($lvprice_data[$member['levelid']])){
								if(isset($lvprice_data[$member['levelid']]['money_price']))
								$result[$k2]['money_price'] = $lvprice_data[$member['levelid']]['money_price'];
								if(isset($lvprice_data[$member['levelid']]['score_price']))
								$result[$k2]['score_price'] = $lvprice_data[$member['levelid']]['score_price'];
							}
						}
						}
					$pagecontent[$k]['data'] = $result;
				}
			}elseif($v['temp'] == 'cycle'){//产品列表 获取产品信息
                if($v['params']['productfrom'] == 0){//手动选择
                    $newdata = array();
                    foreach($v['data'] as $pro){
                        $field = 'id proid,name,pic,market_price,sell_price,sales,ps_cycle';

                        $newpro = Db::name('cycle_product')->field($field)->where('aid',$aid)->where('id',$pro['proid'])->where('status',1)->where('ischecked',1)->find();
                        if($newpro){
                            $newpro['id'] = $pro['id'];
                            $newdata[] = $newpro;
                        }
                    }
                    $pagecontent[$k]['data'] = $newdata;
                }else{
                    $where = [];
                    $where[] = ['aid','=',$aid];
                    $where[] = ['status','=',1];
                    $where[] = ['ischecked','=',1];
                    if($v['params']['category']){
                        $cid = intval($v['params']['category']);
                        $chidlc = Db::name('cycle_category')->where('aid',$aid)->where('pid',$cid)->select()->toArray();
                        if($chidlc){
                            $cids = array($cid);
                            foreach($chidlc as $c){
                                $cids[] = intval($c['id']);
                            }
                            $where[] = ['cid','in',$cids];
                        }else{
                            $where[] = ['cid','=',$cid];
                        }
                    }
                    if($v['params']['bid']!=='' && $v['params']['bid']!==null){
                        $where[] = ['bid','=',$v['params']['bid']];
                    }
                    if($v['params']['group']){
                        $_string = array();
                        foreach($v['params']['group'] as $gid=>$istrue){
                            if($istrue=='true'){
                                if($gid == '0'){
                                    $_string[] = "gid is null or gid=''";
                                }else{
                                    $_string[] = "find_in_set({$gid},gid)";
                                }
                            }
                        }
                        if(!$_string){
                            $where2 = '0=1';
                        }else{
                            $where2 = implode(" or ",$_string);
                        }
                    }else{
                        $where2 = '1=1';
                    }
                    $order = 'sort desc';
                    if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
                    if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
                    if($v['params']['sortby'] == 'createtime') $order = 'createtime';
                    if($v['params']['sortby'] == 'sales') $order = 'sales desc,sort desc';
                    if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
                    $field = 'id proid,name,pic,sell_price,market_price,sales,ps_cycle';

                    $result = Db::name('cycle_product')->field($field)->where($where)->where($where2)->order($order)->limit($v['params']['proshownum'])->select()->toArray();
                    if(!$result) $result = array();

                    foreach($result as $k2=>$v2){
                        $result[$k2]['id'] = 'G'.time().rand(10000000,99999999);
                        $ps_cycle = ['1' => '每日一期','2' => '每周一期' ,'3' => '每月一期'];
                        $result[$k2]['pspl'] = $ps_cycle[$v2['ps_cycle']];
                    }
                    $pagecontent[$k]['data'] = $result;
                }
            }elseif($v['temp'] == 'collage'){//产品列表 获取产品信息
				if($v['params']['productfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $pro){
						$field = 'id proid,name,pic,market_price,sell_price,sales,stock,teamnum';
						$newpro = Db::name('collage_product')->field($field)->where('aid',$aid)->where('id',$pro['proid'])->where('status',1)->where('ischecked',1)->find();
						if($newpro){
							$newpro['id'] = $pro['id'];
							$newdata[] = $newpro;
						}
					}
					$pagecontent[$k]['data'] = $newdata;
				}else{
					$where = [];
					$where[] = ['aid','=',$aid];
					$where[] = ['status','=',1];
					$where[] = ['ischecked','=',1];
					if($v['params']['category']){
						$cid = intval($v['params']['category']);
						$chidlc = Db::name('collage_category')->where('aid',$aid)->where('pid',$cid)->select()->toArray();
						if($chidlc){
							$cids = array($cid);
							foreach($chidlc as $c){
								$cids[] = intval($c['id']);
							}
							$where[] = ['cid','in',$cids];
						}else{
							$where[] = ['cid','=',$cid];
						}
					}
					if($v['params']['bid']!=='' && $v['params']['bid']!==null){
						$where[] = ['bid','=',$v['params']['bid']];
					}
					if($v['params']['group']){
						$_string = array();
						foreach($v['params']['group'] as $gid=>$istrue){
							if($istrue=='true'){
								if($gid == '0'){
									$_string[] = "gid is null or gid=''";
								}else{
									$_string[] = "find_in_set({$gid},gid)";
								}
							}
						}
						if(!$_string){
							$where2 = '0=1';
						}else{
							$where2 = implode(" or ",$_string);
						}
					}else{
						$where2 = '1=1';
					}
					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'createtime') $order = 'createtime';
					if($v['params']['sortby'] == 'sales') $order = 'sales desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
					$field = 'id proid,name,pic,sell_price,market_price,sales,stock,teamnum';
					$result = Db::name('collage_product')->field($field)->where($where)->where($where2)->order($order)->limit($v['params']['proshownum'])->select()->toArray();
					if(!$result) $result = array();
					foreach($result as $k2=>$v2){
						$result[$k2]['id'] = 'G'.time().rand(10000000,99999999);
						}
					$pagecontent[$k]['data'] = $result;
				}
			}elseif($v['temp'] == 'kanjia'){//产品列表 获取产品信息
				if($v['params']['productfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $pro){
						$field = 'id proid,name,pic,sell_price,min_price,sales';
						$newpro = Db::name('kanjia_product')->field($field)->where('aid',$aid)->where('id',$pro['proid'])->where('status',1)->where('ischecked',1)->find();
						if($newpro){
							$newpro['id'] = $pro['id'];
							$newdata[] = $newpro;
						}
					}
					$pagecontent[$k]['data'] = $newdata;
				}else{
					$where = [];
					$where[] = ['aid','=',$aid];
					$where[] = ['status','=',1];
					$where[] = ['ischecked','=',1];
					if($v['params']['category']){

					}
					if($v['params']['bid']!=='' && $v['params']['bid']!==null){
						$where[] = ['bid','=',$v['params']['bid']];
					}
					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'createtime') $order = 'createtime';
					if($v['params']['sortby'] == 'sales') $order = 'sales desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
					$field = 'id proid,name,pic,sell_price,min_price,sales';
					$result = Db::name('kanjia_product')->field($field)->where($where)->order($order)->limit($v['params']['proshownum'])->select()->toArray();
					if(!$result) $result = array();
					foreach($result as $k2=>$v2){
						$result[$k2]['id'] = 'G'.time().rand(10000000,99999999);
						}
					$pagecontent[$k]['data'] = $result;
				}
			}elseif($v['temp'] == 'seckill'){//产品列表 获取产品信息
				if($v['params']['productfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $pro){
						$field = 'id proid,name,pic,sell_price,market_price,sales,stock,seckill_date,seckill_time,starttime';
						$newpro = Db::name('seckill_product')->field($field)->where('aid',$aid)->where('id',$pro['proid'])->where('status',1)->where('ischecked',1)->find();
						if($newpro){
							$newpro['id'] = $pro['id'];
							$newdata[] = $newpro;
						}
					}
					$pagecontent[$k]['data'] = $newdata;
				}else{
                    $set = Db::name('seckill_sysset')->where('aid',aid)->find();
                    $duration = $set['duration'];
					$where = [];
					$where[] = ['aid','=',$aid];
					$where[] = ['status','=',1];
					$where[] = ['ischecked','=',1];
					if($v['params']['category']){

					}
					if($v['params']['bid']!=='' && $v['params']['bid']!==null){
						$where[] = ['bid','=',$v['params']['bid']];
					}
                    if($v['shopstyle'] == 2){
                        //风格2只显示当前这一场的秒杀
                        $timeset = explode(',',$set['timeset']);
                        $hour = date('H');
                        $current = '';
                        foreach ($timeset as $k2 => $item){
                            if(($hour >= $item && $hour < $timeset[$k2]) || ($hour >= $item && $k2 == (count($timeset)-1))){
                                $current = $item;break;
                            }
                        }
                        $where[] = ['seckill_date','=',date('Y-m-d')];
                        $where[] = ['seckill_time','=',$current];
                        //距离结束时间
                    }
					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'createtime') $order = 'createtime';
					if($v['params']['sortby'] == 'sales') $order = 'sales desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
					$field = 'id proid,name,pic,sell_price,market_price,sales,stock,seckill_date,seckill_time,starttime';
					$result = Db::name('seckill_product')->field($field)->where($where)->order($order)->limit($v['params']['proshownum'])->select()->toArray();
					if(!$result) $result = array();
					else {
                        $nowtime = time();
                        foreach($result as $k2=>$v2){
                            $result[$k2]['id'] = 'G'.time().rand(10000000,99999999);
                            //倒计时
                            $seckill_endtime = $v2['starttime'] + $duration * 3600;
                            if($seckill_endtime < $nowtime) {//已结束
                                $result[$k2]['seckill_status'] = 2;
                                $result[$k2]['hour'] = 0;
                                $result[$k2]['minute'] = 0;
                                $result[$k2]['second'] = 0;
                            }else{
                                if($v2['starttime'] > $nowtime){ //未开始
                                    $result[$k2]['seckill_status'] = 0;
                                    $lefttime = $v2['starttime'] - $nowtime;
                                    $result[$k2]['hour'] = floor($lefttime / 3600);
                                    $result[$k2]['minute'] = floor(($lefttime - $result[$k2]['hour'] * 3600) / 60);
                                    $result[$k2]['second'] = $lefttime - ($result[$k2]['hour'] * 3600) - ($result[$k2]['minute'] * 60);
                                    //带天数
                                    $result[$k2]['day'] = floor($lefttime / 86400);
                                    $result[$k2]['day_hour'] = floor(($lefttime - $result[$k2]['day'] * 86400) / 3600);
                                }else{ //进行中
                                    $result[$k2]['seckill_status'] = 1;
                                    $lefttime = $seckill_endtime - $nowtime;
                                    $result[$k2]['hour'] = floor($lefttime / 3600);
                                    $result[$k2]['minute'] = floor(($lefttime - $result[$k2]['hour'] * 3600) / 60);
                                    $result[$k2]['second'] = $lefttime - ($result[$k2]['hour'] * 3600) - ($result[$k2]['minute'] * 60);//带天数
                                    $result[$k2]['day'] = floor($lefttime / 86400);
                                    $result[$k2]['day_hour'] = floor(($lefttime - $result[$k2]['day'] * 86400) / 3600);
                                }
                            }
                            }
                        }

					$pagecontent[$k]['data'] = $result;
				}
			}elseif($v['temp'] == 'tuangou'){//产品列表 团购商品
				if($v['params']['productfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $pro){
						$field = 'id proid,name,pic,sell_price,sales,pricedata';
						$newpro = Db::name('tuangou_product')->field($field)->where('aid',$aid)->where('id',$pro['proid'])->where('status',1)->where('ischecked',1)->find();
						if($newpro){
							$newpro['id'] = $pro['id'];
							$buynum = $newpro['sales'];
							$pricedata = json_decode($newpro['pricedata'],true);
							$nowpricedata = array('num'=>0,'money'=>$newpro['sell_price']);
							foreach($pricedata as $k3=>$v3){
								if($buynum >= $v3['num']){
									$nowpricedata = $v3;
								}
							}
							$newpro['sell_price'] = $nowpricedata['money'];
							$minpricedata = end($pricedata);
							$min_price = $minpricedata['money'];
							$newpro['min_price'] = $min_price;

							$newdata[] = $newpro;
						}
					}
				}else{
					$where = [];
					$where[] = ['aid','=',$aid];
					$where[] = ['status','=',1];
					$where[] = ['ischecked','=',1];
					if($v['params']['category']){
						$cid = intval($v['params']['category']);
						$chidlc = Db::name('tuangou_category')->where('aid',$aid)->where('pid',$cid)->select()->toArray();
						if($chidlc){
							$cids = array($cid);
							foreach($chidlc as $c){
								$cids[] = intval($c['id']);
							}
							$where[] = ['cid','in',$cids];
						}else{
							$where[] = ['cid','=',$cid];
						}
					}
					if($v['params']['bid']!=='' && $v['params']['bid']!==null){
						$where[] = ['bid','=',$v['params']['bid']];
					}

					$where3 = "find_in_set('-1',showtj)";
					if($member){
						$where3.= " or find_in_set('".$member['levelid']."',showtj)";
						if($member['subscribe']==1){
							$where3 .= " or find_in_set('0',showtj)";
						}
					}

					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'createtime') $order = 'createtime';
					if($v['params']['sortby'] == 'sales') $order = 'sales desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
					$field = 'id proid,name,pic,sell_price,sales,pricedata';
					$newdata = Db::name('tuangou_product')->field($field)->where($where)->where($where3)->order($order)->limit($v['params']['proshownum'])->select()->toArray();
					if(!$newdata) $newdata = array();
					foreach($newdata as $k2=>$v2){
						$v2['id'] = 'G'.time().rand(10000000,99999999);
						$buynum = $v2['sales'];
						$pricedata = json_decode($v2['pricedata'],true);
						$nowpricedata = array('num'=>0,'money'=>$v2['sell_price']);
						foreach($pricedata as $k3=>$v3){
							if($buynum >= $v3['num']){
								$nowpricedata = $v3;
							}
						}
						$v2['sell_price'] = $nowpricedata['money'];
						$minpricedata = end($pricedata);
						$min_price = $minpricedata['money'];
						$v2['min_price'] = $min_price;
						$newdata[$k2] = $v2;
						}
					}
				$pagecontent[$k]['data'] = $newdata;
			}elseif($v['temp'] == 'kecheng'){//产品列表 获取产品信息
				if($v['params']['productfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $pro){
						$newpro = Db::name('kecheng_list')->field('id proid,name,pic,market_price,price,lvprice,lvprice_data,join_num,kctype')->where('aid',$aid)->where('id',$pro['proid'])->where('status',1)->find();
						if($newpro){
							$newpro['id'] = $pro['id'];
							if($member){
								if($newpro['lvprice']==1){
									$lvprice_data = json_decode($newpro['lvprice_data'],true);
									if($lvprice_data && isset($lvprice_data[$member['levelid']])){
										$newpro['price'] = $lvprice_data[$member['levelid']]['money_price'];
									}
								}else{
									$memberlevel = Db::name('member_level')->where('id',$member['levelid'])->find();
									if($memberlevel['kecheng_discount'] > 0 && $memberlevel['kecheng_discount'] < 10){
										$newpro['market_price'] = $newpro['price'];
										$newpro['price'] = $newpro['price'] * $memberlevel['kecheng_discount'] * 0.1;
									}
								}
							}
							$newpro['count'] = Db::name('kecheng_chapter')->where('kcid',$pro['proid'])->where('status',1)->count();
							$newdata[] = $newpro;
						}
					}
					$pagecontent[$k]['data'] = $newdata;
				}else{
					$where = [];
					$where[] = ['aid','=',$aid];
					if($v['params']['category']){
						$cid = intval($v['params']['category']);
						$chidlc = Db::name('kecheng_category')->where('aid',$aid)->where('pid',$cid)->select()->toArray();
						if($chidlc){
							$cids = array($cid);
							foreach($chidlc as $c){
								$cids[] = intval($c['id']);
							}
							$where[] = ['cid','in',$cids];
						}else{
							$where[] = ['cid','=',$cid];
						}
					}
					if($v['params']['bid']!=='' && $v['params']['bid']!==null){
						$where[] = ['bid','=',$v['params']['bid']];
					}
					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'createtime') $order = 'createtime';
					if($v['params']['sortby'] == 'sales') $order = 'join_num desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
					$result = Db::name('kecheng_list')->field('id proid,name,pic,price,lvprice,lvprice_data,market_price,join_num,sort,kctype')->where($where)->order($order)->limit($v['params']['proshownum'])->select()->toArray();
					if(!$result) $result = array();
					foreach($result as $k2=>$v2){
						$result[$k2]['id'] = 'G'.time().rand(10000000,99999999);
						if($member){
							if($v2['lvprice']==1){
								$lvprice_data = json_decode($v2['lvprice_data'],true);
								if($lvprice_data && isset($lvprice_data[$member['levelid']])){
									$result[$k2]['price'] = $lvprice_data[$member['levelid']]['money_price'];
								}
							}else{
								$memberlevel = Db::name('member_level')->where('id',$member['levelid'])->find();
								if($memberlevel['kecheng_discount'] > 0 && $memberlevel['kecheng_discount'] < 10){
									$result[$k2]['market_price'] = $v2['price'];
									$result[$k2]['price'] = $v2['price'] * $memberlevel['kecheng_discount'] * 0.1;
								}
							}
						}
						$result[$k2]['count'] = Db::name('kecheng_chapter')->where('kcid',$v2['proid'])->where('status',1)->count();
					}
					$pagecontent[$k]['data'] = $result;
				}
            }elseif($v['temp'] == 'luckycollage'){//产品列表 获取产品信息
				if($v['params']['productfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $pro){
						$field = 'id proid,name,pic,sell_price,market_price,sales,teamnum,gua_num,fy_money,fy_money_val,fy_type';
						$newpro = Db::name('lucky_collage_product')->field($field)->where('aid',$aid)->where('id',$pro['proid'])->where('status',1)->where('ischecked',1)->find();
						if($newpro){
							$newpro['money'] = round($newpro['fy_money_val'],2);
							if($newpro['fy_type']==1){
								$newpro['money'] = round($newpro['fy_money']*$newpro['sell_price']/100,2);
							}

							$newpro['linktype'] = 0;
							$newpro['id'] = $pro['id'];
							$newdata[] = $newpro;
						}

					}
					$pagecontent[$k]['data'] = $newdata;
				}else{
					$where = [];
					$where[] = ['aid','=',$aid];
					$where[] = ['status','=',1];
					$where[] = ['ischecked','=',1];
					if($v['params']['category']){
						$cid = intval($v['params']['category']);
						$chidlc = Db::name('lucky_collage_category')->where('aid',$aid)->where('pid',$cid)->select()->toArray();
						if($chidlc){
							$cids = array($cid);
							foreach($chidlc as $c){
								$cids[] = intval($c['id']);
							}
							$where[] = ['cid','in',$cids];
						}else{
							$where[] = ['cid','=',$cid];
						}
					}
					if($v['params']['bid']!=='' && $v['params']['bid']!==null){
						$where[] = ['bid','=',$v['params']['bid']];
					}
					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'createtime') $order = 'createtime';
					if($v['params']['sortby'] == 'sales') $order = 'sales desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
					$field = 'id proid,name,pic,sell_price,market_price,sales,teamnum,gua_num,fy_money,fy_money_val,fy_type';
					$result = Db::name('lucky_collage_product')->field($field)->where($where)->order($order)->limit($v['params']['proshownum'])->select()->toArray();
					if(!$result) $result = array();
					foreach($result as $k2=>$v2){
						$result[$k2]['id'] = 'G'.time().rand(10000000,99999999);
						$result[$k2]['money'] = round($v2['fy_money_val'],2);
						if($v2['fy_type']==1){
							$result[$k2]['money'] = round($v2['fy_money']*$v2['sell_price']/100,2);
						}
						$result[$k2]['linktype'] = 0;
						}
					$pagecontent[$k]['data'] = $result;
				}
			}elseif($v['temp'] == 'yuyue'){//预约服务 获取产品信息
				if($v['params']['productfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $pro){
						$newpro = Db::name('yuyue_product')->field('id proid,name,pic,sell_price,sales,danwei')->where('aid',$aid)->where('id',$pro['proid'])->where('status',1)->where('ischecked',1)->find();
						if($newpro){
							$newpro['id'] = $pro['id'];
							$newdata[] = $newpro;
						}
					}
				}else{
					$where = [];
					$where[] = ['aid','=',$aid];
					//$where[] = ['status','=',1];
					$where[] = ['ischecked','=',1];
					$nowtime = time();
					$nowhm = date('H:i');
					$where[] = Db::raw("`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime)");
					if($v['params']['category']){
						$cid = intval($v['params']['category']);
						$chidlc = Db::name('yuyue_category')->where('aid',$aid)->where('pid',$cid)->select()->toArray();
						if($chidlc){
							$cids = array($cid);
							foreach($chidlc as $c){
								$cids[] = intval($c['id']);
							}
							$where[] = ['cid','in',$cids];
						}else{
							$where[] = ['cid','=',$cid];
						}
					}
					if($v['params']['bid']!=='' && $v['params']['bid']!==null){
						$where[] = ['bid','=',$v['params']['bid']];
					}
					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'createtime') $order = 'createtime';
					if($v['params']['sortby'] == 'sales') $order = 'sales desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
					$newdata = Db::name('yuyue_product')->field('id proid,name,pic,sell_price,sales,danwei')->where($where)->order($order)->limit($v['params']['proshownum'])->select()->toArray();
					if(!$newdata) $newdata = array();
                    foreach($newdata as $k2=>$v2){
                        $newdata[$k2]['id'] = 'G'.time().rand(10000000,99999999);
                    }
                    $pagecontent[$k]['data'] = $result;
				}
				$pagecontent[$k]['data'] = $newdata;
			}elseif($v['temp'] == 'daihuobiji'){ // 短视频
    $newdata = array();
    $midList = []; // 用于存储所有member的id

    if($v['params']['productfrom'] == 0){ // 手动选择
        foreach($v['data'] as $art){
            $newart = Db::name('daihuobiji')
                ->field('id daihuobijiId, mid, title, content, coverimg, createtime, readcount, zan, bid')
                ->where('aid', $aid)
                ->where('id', $art['videoId'])
                ->where('status', 1)
                ->find();
            if($newart){
                $newart['id'] = $art['id'];
                $newdata[] = $newart;
                $midList[] = $newart['mid']; // 收集member的id
            }
        }
    }
    else{
        $where = [];
        $where[] = ['aid', '=', $aid];
        $where[] = ['status', '=', 1];
        if($v['params']['category']){
            $cid = intval($v['params']['category']);
            $where[] = ['cid', '=', $cid];
        }
        if($v['params']['bid'] !== '' && $v['params']['bid'] !== null){
            $where[] = ['bid', '=', $v['params']['bid']];
        }
        $order = 'sort desc';
        if($v['params']['sortby'] == 'sort') $order = 'sort desc, id desc';
        if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
        if($v['params']['sortby'] == 'createtime') $order = 'createtime';
        if($v['params']['sortby'] == 'viewnum') $order = 'view_num desc, sort desc';
        if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
        
        $newdata = Db::name('daihuobiji')
            ->field('id daihuobijiId, mid, title, content, coverimg, createtime, readcount, zan, bid')
            ->where($where)
            ->order($order)
            ->limit($v['params']['shownum'])
            ->select()
            ->toArray();
        
        if(!$newdata) $newdata = array();
        
        foreach($newdata as $k2 => $v2){
            $newdata[$k2]['id'] = 'G' . time() . rand(10000000,99999999);
            $midList[] = $v2['mid']; // 收集member的id
        }
    }

    // 去重member的id
    $midList = array_unique($midList);

    // 批量查询member表获取最新的nickname和headimg
    $memberMap = [];
    if(!empty($midList)){
        $members = Db::name('member')
            ->field('id, nickname, headimg') // 使用id代替mid
            ->whereIn('id', $midList) // 查询条件改为id
            ->select()
            ->toArray();
        
        // 构建id到member信息的映射
        foreach($members as $member){
            $memberMap[$member['id']] = [
                'nickname' => $member['nickname'],
                'headimg' => $member['headimg'],
            ];
        }
    }

    // 替换newdata中的nickname和headimg
    foreach($newdata as $k2 => $v2){
        if(isset($memberMap[$v2['mid']])){ // 使用memberMap中的id
            $newdata[$k2]['nickname'] = $memberMap[$v2['mid']]['nickname'];
            $newdata[$k2]['headimg'] = $memberMap[$v2['mid']]['headimg'];
        }

        // 处理logo
        if($v2['bid'] != 0){
            $newdata[$k2]['logo'] = Db::name('business')
                ->where('aid', $aid)
                ->where('id', $v2['bid'])
                ->value('logo');
        } else {
            $newdata[$k2]['logo'] = Db::name('admin_set')
                ->where('aid', $aid)
                ->value('logo');
        }
    }

    $pagecontent[$k]['data'] = $newdata;
}

			elseif($v['temp'] == 'video'){//视频模板
				// 处理视频模板的参数，确保自动播放等参数正确传递
				// 设置默认值
				if(!isset($v['params']['autoplay'])) $v['params']['autoplay'] = false;
				if(!isset($v['params']['muted'])) $v['params']['muted'] = false;
				if(!isset($v['params']['loop'])) $v['params']['loop'] = false;
				if(!isset($v['params']['controls'])) $v['params']['controls'] = true;

				// 确保布尔值类型正确
				$v['params']['autoplay'] = (bool)$v['params']['autoplay'];
				$v['params']['muted'] = (bool)$v['params']['muted'];
				$v['params']['loop'] = (bool)$v['params']['loop'];
				$v['params']['controls'] = $v['params']['controls'] !== false;

				// 更新参数到pagecontent
				$pagecontent[$k]['params'] = $v['params'];
			}
			elseif($v['temp'] == 'shortvideo'){//短视频
				if($v['params']['productfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $art){
						$newart = Db::name('shortvideo')->field('id videoId,name,description,coverimg,view_num,zan_num,createtime')->where('aid',$aid)->where('id',$art['videoId'])->where('status',1)->find();
						if($newart){
							$newart['id'] = $art['id'];
							$newdata[] = $newart;
						}
					}
				}
				else{
					$where = [];
					$where[] = ['aid','=',$aid];
					$where[] = ['status','=',1];
					if($v['params']['category']){
						$cid = intval($v['params']['category']);
						$where[] = ['cid','=',$cid];
					}
					if($v['params']['bid']!=='' && $v['params']['bid']!==null){
						$where[] = ['bid','=',$v['params']['bid']];
					}
					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'createtime') $order = 'createtime';
					if($v['params']['sortby'] == 'viewnum') $order = 'view_num desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
					$newdata = Db::name('shortvideo')->field('id videoId,name,description,coverimg,view_num,zan_num,createtime')->where($where)->order($order)->limit($v['params']['shownum'])->select()->toArray();
					if(!$newdata) $newdata = array();
					foreach($newdata as $k2=>$v2){
						$newdata[$k2]['id'] = 'G'.time().rand(10000000,99999999);
					}
				}
				foreach($newdata as $k2=>$v){
					if($v['bid']!=0){
						$newdata[$k2]['logo'] = Db::name('business')->where('aid',aid)->where('id',$v['bid'])->value('logo');
					} else {
						$newdata[$k2]['logo'] = Db::name('admin_set')->where('aid',aid)->value('logo');
					}
				}
				$pagecontent[$k]['data'] = $newdata;
			}
			//带货团购列表
			elseif($v['temp'] == 'daihuoyiuan'){
				//手动选择
				if($v['params']['productfrom'] == 0){
					$newdata = array();
					foreach($v['data'] as $pro){
						$newart = Db::name('daihuoyiuan')->field('id as proid,aid,author,name,pic,pic2,readcount,status,createtime,productids')->where('aid',$aid)->where('id',$pro['id'])->where('status',1)->find();
						$newpro['id'] = $pro['proid'];

						$newart['createtime'] = date('Y-m-d',$newart['createtime']);
						// 保留 'pic2' 为原始的逗号分隔的字符串形式，并限制最多三张图片
						if(isset($newart['pic2']) && $newart['pic2']){
						    $pic2_arr = explode(',', $newart['pic2']);
						    $newart['pic2'] = implode(',', array_slice($pic2_arr, 0, 3));
						} else {
						    $newart['pic2'] = '';
						}

						// 获取当前笔记的产品ID列表
						$productIds = explode(',', $newart['productids']);
						$products = Db::name('shop_product')->whereIn('id', $productIds)->field('sell_price')->select()->toArray();

						// 计算该笔记的价格范围
						$prices = array_column($products, 'sell_price');
						if(!empty($prices)){
						    $minPrice = min($prices);
						    $maxPrice = max($prices);
						    $newart['priceRange'] = ($minPrice == $maxPrice) ? $minPrice : "{$minPrice}~{$maxPrice}";
						} else {
						    $newart['priceRange'] = '';
						}

						$newdata[] = $newart;
					}
				}else{
					$where = [
						['aid','=', $aid],
						['status','=', 1],
						['bid', '=', !empty($v['params']['bid'])? $v['params']['bid'] : 0]
					];
					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'createtime') $order = 'createtime';
					// if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
					$result = Db::name('daihuoyiuan')->field('id as proid,aid,author,name,pic,pic2,readcount,status,createtime,productids')->where($where)->order($order)->limit($v['params']['shownum'])->select()->toArray();
					foreach($result as $k2=>$v2){
						$result[$k2]['id'] = 'G'.time().rand(10000000,99999999);

					    $result[$k2]['createtime'] = date('Y-m-d',$v['createtime']);
					    
					    // 保留 'pic2' 为原始的逗号分隔的字符串形式，并限制最多三张图片
					    if(isset($v2['pic2']) && $v2['pic2']){
					        $pic2_arr = explode(',', $v2['pic2']);
					        // $result[$k2]['pic2'] = implode(',', array_slice($pic2_arr, 0, 3));
					        $result[$k2]['pic2'] = explode(',', implode(',', array_slice($pic2_arr, 0, 3)));
					    } else {
					        $result[$k2]['pic2'] = '';
					    }

					    // 获取当前笔记的产品ID列表
					    $productIds = explode(',', $v2['productids']);
					    $products = Db::name('shop_product')
					                  ->whereIn('id', $productIds)
					                  ->field('sell_price')
					                  ->select()
					                  ->toArray();

					    // 计算该笔记的价格范围
					    $prices = array_column($products, 'sell_price');
					    if(!empty($prices)){
					        $minPrice = min($prices);
					        $maxPrice = max($prices);
					        $result[$k2]['priceRange'] = ($minPrice == $maxPrice) ? $minPrice : "{$minPrice}~{$maxPrice}";
					    } else {
					        $result[$k2]['priceRange'] = '';
					    }
					}

					$pagecontent[$k]['data'] = $result;
				}
			}
			elseif($v['temp'] == 'liveroom'){//直播列表
				if($v['params']['liveroomfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $pro){
						$newpro = Db::name('live_room')->field('id,bid,roomId,name,coverImg,shareImg,startTime,endTime,anchorName')->where('aid',$aid)->where('roomId',$pro['roomId'])->where('status',1)->find();
						if($newpro){
							$newpro['id'] = $pro['id'];
							$newpro['commentscore'] = floor($newpro['comment_score']);
							$newpro['startTime'] = date('m-d H:i',$newpro['startTime']);
							$newpro['endTime'] = date('m-d H:i',$newpro['endTime']);
							$newpro['status'] = 1;
							if($newpro['startTime'] > time()){ //未开始
								$newpro['status'] = 0;
								if(date('Y-m-d') == date('Y-m-d',$newpro['startTime'])){
									$newpro['showtime'] = '今天'.date('H:i',$newpro['startTime']).'开播';
								}elseif(date('Y-m-d',time()+86400) == date('Y-m-d',$newpro['startTime'])){
									$newpro['showtime'] = '明天'.date('H:i',$newpro['startTime']).'开播';
								}elseif(date('Y-m-d',time()+86400*2) == date('Y-m-d',$newpro['startTime'])){
									$newpro['showtime'] = '后天'.date('H:i',$newpro['startTime']).'开播';
								}else{
									$newpro['showtime'] = date('m-d H:i',$newpro['startTime']).'开播';
								}
							}
							if($newpro['endTime'] < time()){ //已结束
								$newpro['status'] = 2;
							}

							$newdata[] = $newpro;
						}
					}
					$pagecontent[$k]['data'] = $newdata;
				}else{
					$where = [];
					$where[] = ['aid','=',$aid];
					$where[] = ['status','=',1];
					if($v['params']['category']){
						$cid = intval($v['params']['category']);
						$where[] = ['cid','=',$cid];
					}
					$order = 'roomId desc';
					if($v['params']['sortby'] == 'sort') $order = 'roomId desc';
					if($v['params']['sortby'] == 'starttimedesc') $order = 'startTime desc';
					if($v['params']['sortby'] == 'starttime') $order = 'startTime';
					if($v['params']['sortby'] == 'endtimedesc') $order = 'endTime desc';
					if($v['params']['sortby'] == 'endtime') $order = 'endTime';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
					$result = Db::name('live_room')->field('id,bid,roomId,name,coverImg,shareImg,startTime,endTime,anchorName')->where($where)->order($order)->limit($v['params']['shownum'])->select()->toArray();
					if(!$result) $result = array();
					foreach($result as $k2=>$v2){
						$result[$k2]['id'] = 'G'.time().rand(10000000,99999999);
						$result[$k2]['startTime'] = date('m-d H:i',$v2['startTime']);
						$result[$k2]['endTime'] = date('m-d H:i',$v2['endTime']);
						$result[$k2]['status'] = 1;
						if($v2['startTime'] > time()){ //未开始
							$result[$k2]['status'] = 0;
							if(date('Y-m-d') == date('Y-m-d',$v2['startTime'])){
								$result[$k2]['showtime'] = '今天'.date('H:i',$v2['startTime']).'开播';
							}elseif(date('Y-m-d',time()+86400) == date('Y-m-d',$v2['startTime'])){
								$result[$k2]['showtime'] = '明天'.date('H:i',$v2['startTime']).'开播';
							}elseif(date('Y-m-d',time()+86400*2) == date('Y-m-d',$v2['startTime'])){
								$result[$k2]['showtime'] = '后天'.date('H:i',$v2['startTime']).'开播';
							}else{
								$result[$k2]['showtime'] = date('m-d H:i',$v2['startTime']).'开播';
							}
						}
						if($v2['endTime'] < time()){ //已结束
							$result[$k2]['status'] = 2;
						}
					}
					$pagecontent[$k]['data'] = $result;
				}
			}elseif($v['temp'] == 'business'){//商家列表
				if($v['params']['businessfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $pro){
						$newpro = Db::name('business')->field('id bid,name,logo,address,comment_score,sales,content')->where('aid',$aid)->where('id',$pro['bid'])->where('status',1)->where('is_open',1)->find();
						if($newpro){
							$newpro['id'] = $pro['id'];
							$newpro['commentscore'] = floor($newpro['comment_score']);
							$newdata[] = $newpro;
						}
					}
					$pagecontent[$k]['data'] = $newdata;
				}else{
					$where = [];
                    $where_str = '';
					$where[] = ['aid','=',$aid];
					$where[] = ['status','=',1];
					$where[] = ['is_open','=',1];
					if($v['params']['category']){
						$cid = intval($v['params']['category']);
						//$where[] = ['cid','=',$cid];
                        //$where_str .= " and cid={$cid}";
						$where[] = Db::raw("find_in_set({$cid},cid)");
                        $where_str .= " and find_in_set({$cid},cid)";
					}
					
					
					//区域限制
        			$area_set = Db::name('admin_set')->where('aid',aid)->field('area_on,areamode')->find();
        			$area_id = input('param.area_id/d');
        			if($area_set && $area_set['area_on'] && $area_id){
        			    $permission = Db::name('admin_set_area_permission')->where(['a_area'=>md5(aid.$area_id)])->value('permission');
        			    //var_dump($permission);
        			    if($permission){
            			    $permission = json_decode($permission,true);
            			    if($area_set['areamode']==0)$limit_field = 'province_id';
            			    if($area_set['areamode']==1)$limit_field = 'city_id';
            			    if($area_set['areamode']==2)$limit_field = 'district_id';
            			    if($permission['client_visible_area']==0){
            			        //仅可见该区域
            			        $where[] = [$limit_field,'=',$area_id];
            			    }else if($permission['client_visible_area']==1){
            			        //可见部分区域
            			        $where[] = [$limit_field,'in',explode(',',$permission['client_visible_areas'])];
            			    }else if($permission['client_visible_area']==2){
            			        //不可见部分区域
            			        $where[] = [$limit_field,'notin',explode(',',$permission['client_unvisible_areas'])];
            			    } 
        			    }
        			}
					
					
					
					
					
					
					
					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'createtime') $order = 'createtime';
					if($v['params']['sortby'] == 'sales') $order = 'sales desc,sort desc';
					if($v['params']['sortby'] == 'scoredesc') $order = 'comment_score desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');

					$filedraw = '';
					if($v['params']['sortby'] == 'juli' && $latitude && $longitude){
					    if($v['params']['distance']) {
					        //$filedraw .= ",round(( st_distance(point({$longitude}, {$latitude}),point(longitude, latitude)) / 0.0111 ) * 1000) AS distance";
							$filedraw .= ",round(6378.138*2*asin(sqrt(pow(sin( ({$latitude}*pi()/180-latitude*pi()/180)/2),2)+cos({$latitude}*pi()/180)*cos(latitude*pi()/180)* pow(sin( ({$longitude}*pi()/180-longitude*pi()/180)/2),2)))*1000) AS distance";
							//\think\facade\Log::write($filedraw);
                            $order = "distance asc";
                            $result = Db::query("select * from ((select id bid,name,logo,address,comment_score,sales,content,longitude,latitude".$filedraw." from ".table_name('business').
                                " where aid=:aid and status = 1 ".$where_str." order by $order ) as A) where distance <= :distance limit ".$v['params']['shownum'], ['aid' => $aid, 'distance' => $v['params']['distance'] * 1000]);
                        } else {
                            $order = Db::raw("({$longitude}-longitude)*({$longitude}-longitude) + ({$latitude}-latitude)*({$latitude}-latitude) ");
                            $result = Db::name('business')->fieldRaw('id bid,name,logo,address,comment_score,sales,content,longitude,latitude'.$filedraw)->where($where)->order($order)->limit($v['params']['shownum'])->select()->toArray();
                        }
					} else {
                        $result = Db::name('business')->fieldRaw('id bid,name,logo,address,comment_score,sales,content,longitude,latitude'.$filedraw)->where($where)->order($order)->limit($v['params']['shownum'])->select()->toArray();
                    }
//
					if(!$result) $result = array();
					$nowtime = time();
					$nowhm = date('H:i');
					foreach($result as $k2=>$v2){
						$result[$k2]['id'] = 'G'.time().rand(10000000,99999999);
						$result[$k2]['commentscore'] = floor($v2['comment_score']);
						if($longitude && $latitude){
							$result[$k2]['juli'] = getdistance($longitude,$latitude,$v2['longitude'],$v2['latitude'],2).'km';
						}else{
							$result[$k2]['juli'] = '';
						}
						$statuswhere = "`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime) or (`status`=3 and ((start_hours<end_hours and start_hours<='$nowhm' and end_hours>='$nowhm') or (start_hours>=end_hours and (start_hours<='$nowhm' or end_hours>='$nowhm'))) )";
						$prolist = Db::name('shop_product')->field('id,name,pic,sell_price,price_type,sales,market_price')->where('bid',$v2['bid'])->where('ischecked',1)->where($statuswhere)->limit(8)->order('sales desc,sort desc,id desc')->select()->toArray();
						if(!$prolist) $prolist = [];
						if(count($prolist) < 8){
							$prolist2 = Db::name('yuyue_product')->field('id,name,pic,sell_price,sales,danwei')->where('bid',$v2['bid'])->where('ischecked',1)->where("`status`=1 or (`status`=2 and unix_timestamp(start_time)<=$nowtime and unix_timestamp(end_time)>=$nowtime)")->limit(8-count($prolist))->order('sales desc,sort desc,id desc')->select()->toArray();
							foreach($prolist2 as $pro2){
								$pro2['module'] = 'yuyue';
								$prolist[] = $pro2;
							}
						}
						$result[$k2]['prolist'] = $prolist;
					}
					$pagecontent[$k]['data'] = $result;
				}
			}elseif($v['temp'] == 'zhaopinzhiwei'){//招聘职位列表
				if($v['params']['jobfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $pro){
						$newpro = Db::name('zhaopin_position')
							->field('id zwid,title,salary,education,experience,work_address,company_id,benefits,views,numbers,description,work_mode,payment,work_time_type,work_intensity,options,reward_type,reward_data1,reward_data2,age_requirement,gender_requirement')
							->where('aid',$aid)
							->where('id',$pro['zwid'])
							->where('status',1)
							->find();
							//var_dump($newpro);die;
						if($newpro){
							$newpro['id'] = $pro['id'];
							
							//根据显示控制参数处理数据
							if(!isset($v['params']['showsalary']) || $v['params']['showsalary'] == 0) unset($newpro['salary']);
							if(!isset($v['params']['showeducation']) || $v['params']['showeducation'] == 0) unset($newpro['education']);
							if(!isset($v['params']['showexperience']) || $v['params']['showexperience'] == 0) unset($newpro['experience']);
							if(!isset($v['params']['showaddress']) || $v['params']['showaddress'] == 0) unset($newpro['work_address']);
							if(!isset($v['params']['shownumbers']) || $v['params']['shownumbers'] == 0) unset($newpro['numbers']);
							if(!isset($v['params']['showdetail']) || $v['params']['showdetail'] == 0) unset($newpro['description']);
							
							//获取企业信息
							if(isset($v['params']['showcompany']) && $v['params']['showcompany'] == 1){
								$company = Db::name('zhaopin_company')
									->field('name company_name,logo company_logo')
									->where('id',$newpro['company_id'])
									->find();
								if($company){
									$newpro = array_merge($newpro,$company);
								}
							}

							// 处理工作标签
							$tags = [];
							if($newpro['work_mode']) $tags[] = $newpro['work_mode'];
							if($newpro['payment']) $tags[] = $newpro['payment'];
							if($newpro['work_time_type']) $tags[] = $newpro['work_time_type'];
							if($newpro['work_intensity']) $tags[] = $newpro['work_intensity'];
							// 添加年龄要求标签
							if($newpro['age_requirement']) $tags[] = $newpro['age_requirement'];
							// 添加性别要求标签
							if($newpro['gender_requirement']) {
								switch($newpro['gender_requirement']) {
									case 1:
										$tags[] = '限男';
										break;
									case 2:
										$tags[] = '限女';
										break;
									default:
										$tags[] = '不限性别';
								}
							}
							
							// 处理自定义标签选项
							if($newpro['options']){
								$options = json_decode($newpro['options'], true);
								if(is_array($options)){
									foreach($options as $opt){
										$tags[] = $opt;
									}
								}
							}
							
							$newpro['tags'] = $tags;

							// 计算佣金
							if($mid > 0) {
								$commission_data = self::calculateCommission($newpro, $mid);
								if(!empty($commission_data)) {
									$newpro['commission_data'] = $commission_data;
									// 添加佣金说明
									$salary_range = explode('-', $newpro['salary']);
									$min_salary = intval($salary_range[0]);
									$max_salary = intval($salary_range[1]);
									
									switch($newpro['reward_type']) {
										case 0:
											$newpro['commission_text'] = " {$commission_data['commission_min']}-{$commission_data['commission_max']}";
											break;
										case 1:
											$newpro['commission_text'] = " {$commission_data['commission_min']}-{$commission_data['commission_max']}";
											break;
										case 2:
											$newpro['commission_text'] = "{$commission_data['commission_min']}";
											break;
									}
								}
							}

							$newdata[] = $newpro;
						}
					}
					$pagecontent[$k]['data'] = $newdata;
				}else{
					$where = [];
					$where[] = ['aid','=',$aid];
					$where[] = ['status','=',1];
					
					//分类筛选
					if($v['params']['category']){
						$cid = intval($v['params']['category']);
						$where[] = ['category_id','=',$cid];
					}
					
					//区域限制
					$area_set = Db::name('admin_set')->where('aid',aid)->field('area_on,areamode')->find();
					$area_id = input('param.area_id/d');
					if($area_set && $area_set['area_on'] && $area_id){
						$permission = Db::name('admin_set_area_permission')->where(['a_area'=>md5(aid.$area_id)])->value('permission');
						if($permission){
							$permission = json_decode($permission,true);
							if($area_set['areamode']==0)$limit_field = 'province_id';
							if($area_set['areamode']==1)$limit_field = 'city_id';
							if($area_set['areamode']==2)$limit_field = 'district_id';
							if($permission['client_visible_area']==0){
								//仅可见该区域
								$where[] = [$limit_field,'=',$area_id];
							}else if($permission['client_visible_area']==1){
								//可见部分区域
								$where[] = [$limit_field,'in',explode(',',$permission['client_visible_areas'])];
							}else if($permission['client_visible_area']==2){
								//不可见部分区域
								$where[] = [$limit_field,'notin',explode(',',$permission['client_unvisible_areas'])];
							} 
						}
					}
					
					//排序方式
					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'create_time desc';
					if($v['params']['sortby'] == 'createtime') $order = 'create_time';
					if($v['params']['sortby'] == 'views') $order = 'views desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');

					//构建查询字段
					$fields = ['id zwid'];
					if(!isset($v['params']['showsalary']) || $v['params']['showsalary'] == 1) $fields[] = 'salary';
					if(!isset($v['params']['showeducation']) || $v['params']['showeducation'] == 1) $fields[] = 'education';
					if(!isset($v['params']['showexperience']) || $v['params']['showexperience'] == 1) $fields[] = 'experience';
					if(!isset($v['params']['showaddress']) || $v['params']['showaddress'] == 1) $fields[] = 'work_address';
					if(!isset($v['params']['shownumbers']) || $v['params']['shownumbers'] == 1) $fields[] = 'numbers';
					if(!isset($v['params']['showdetail']) || $v['params']['showdetail'] == 1) $fields[] = 'description';
					$fields[] = 'company_id';
					$fields[] = 'title';
					$fields[] = 'views';
					$fields[] = 'longitude';
					$fields[] = 'latitude';
					$fields[] = 'work_mode';
					$fields[] = 'payment';
					$fields[] = 'work_time_type'; 
					$fields[] = 'work_intensity';
					$fields[] = 'options';
					$fields[] = 'reward_type';
					$fields[] = 'reward_data1';  // 提成比例数据
					$fields[] = 'reward_data2';  // 提成金额数据
					$fields[] = 'age_requirement'; // 年龄要求
					$fields[] = 'gender_requirement'; // 性别要求

					//距离计算
					$filedraw = '';
					if($v['params']['sortby'] == 'distance' && $latitude && $longitude){
						if($v['params']['distance']) {
							$filedraw .= ",round(6378.138*2*asin(sqrt(pow(sin( ({$latitude}*pi()/180-latitude*pi()/180)/2),2)+cos({$latitude}*pi()/180)*cos(latitude*pi()/180)* pow(sin( ({$longitude}*pi()/180-longitude*pi()/180)/2),2)))*1000) AS distance";
							$order = "distance asc";
							$result = Db::query("select * from ((select ".implode(',',$fields).$filedraw." from ".table_name('zhaopin_position').
								" where aid=:aid and status = 1 order by $order ) as A) where distance <= :distance limit ".$v['params']['shownum'], ['aid' => $aid, 'distance' => $v['params']['distance'] * 1000]);
						} else {
							$order = Db::raw("({$longitude}-longitude)*({$longitude}-longitude) + ({$latitude}-latitude)*({$latitude}-latitude) ");
							$result = Db::name('zhaopin_position')
								->field(implode(',',$fields).$filedraw)
								->where($where)
								->order($order)
								->limit($v['params']['shownum'])
								->select()
								->toArray();
						}
					} else {
						$result = Db::name('zhaopin_position')
							->field(implode(',',$fields).$filedraw)
							->where($where)
							->order($order)
							->limit($v['params']['shownum'])
							->select()
							->toArray();
					}

					if(!$result) $result = array();
					foreach($result as $k2=>$v2){
						$result[$k2]['id'] = 'Z'.time().rand(10000000,99999999);
						//获取企业信息
						if(isset($v['params']['showcompany']) && $v['params']['showcompany'] == 1){
							$company = Db::name('zhaopin_company')
								->field('name company_name,logo company_logo')
								->where('id',$v2['company_id'])
								->find();
							if($company){
								$result[$k2] = array_merge($result[$k2],$company);
							}
						}
						//计算距离
						if($longitude && $latitude){
							$result[$k2]['juli'] = getdistance($longitude,$latitude,$v2['longitude'],$v2['latitude'],2).'km';
						}else{
							$result[$k2]['juli'] = '';
						}
						
						//检查是否已收藏
						if($mid > 0) {
							$is_favorite = Db::name('zhaopin_favorite')
								->where('aid', aid)
								->where('position_id', $v2['zwid'])
								->where('mid', $mid)
								->find();
							$result[$k2]['is_favorite'] = $is_favorite ? 1 : 0;

							// 计算佣金
							$commission_data = self::calculateCommission($v2, $mid);
							if(!empty($commission_data)) {
								$result[$k2]['commission_data'] = $commission_data;
								// 添加佣金说明
								$salary_range = explode('-', $v2['salary']);
								$min_salary = intval($salary_range[0]);
								$max_salary = intval($salary_range[1]);
								
								switch($v2['reward_type']) {
									case 0:
										$result[$k2]['commission_text'] = "{$commission_data['commission_min']}-{$commission_data['commission_max']}";
										break;
									case 1:
										$result[$k2]['commission_text'] = "{$commission_data['commission_min']}-{$commission_data['commission_max']}";
										break;
									case 2:
										$result[$k2]['commission_text'] = "{$commission_data['commission_min']}";
										break;
								}
							}
						} else {
							$result[$k2]['is_favorite'] = 0;
						}
						
						// 处理工作标签
						$tags = [];
						if($v2['work_mode']) $tags[] = $v2['work_mode'];
						if($v2['payment']) $tags[] = $v2['payment'];
						if($v2['work_time_type']) $tags[] = $v2['work_time_type'];
						if($v2['work_intensity']) $tags[] = $v2['work_intensity'];
						// 添加年龄要求标签
						if($v2['age_requirement']) $tags[] = $v2['age_requirement'];
						// 添加性别要求标签
						if($v2['gender_requirement']) {
							switch($v2['gender_requirement']) {
								case 1:
									$tags[] = '限男';
									break;
								case 2:
									$tags[] = '限女';
									break;
								default:
									$tags[] = '不限性别';
							}
						}
						
						// 处理自定义标签选项
						if($v2['options']){
							$options = json_decode($v2['options'], true);
							if(is_array($options)){
								foreach($options as $type_id => $option_ids) {
									// 获取标签选项信息
									$option_list = Db::name('zhaopin_options')
										->whereIn('id', $option_ids)
										->where([
											'aid' => $aid,
											'status' => 1
										])
										->field('id,name')
										->select()
										->toArray();
									
									foreach($option_list as $option) {
										$tags[] = $option['name'];
									}
								}
							}
						}
						
						$result[$k2]['tags'] = $tags;
					}
					$pagecontent[$k]['data'] = $result;
				}
			}elseif($v['temp'] == 'article'){//文章列表 获取文章信息
				if($v['params']['articlefrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $art){
                        $where = [];
                        $where[] = ['aid','=',$aid];
                        $where[] = ['status','=',1];
                        $field = 'id artid,name,subname,pic,author,readcount,sort,createtime,sendtime';
                        $newart = Db::name('article')
							->field($field)
							->where($where)->where('id',$art['artid'])
							->find();
						if($newart){
							$newart['id'] = $art['id'];
							$newart['sendtime'] = date('Y-m-d',$newart['createtime']);
							$newart['createtime'] = date('Y-m-d',$newart['createtime']);
							$newdata[] = $newart;
						}
					}
					$pagecontent[$k]['data'] = $newdata;
				}else{
                    $where = [];
                    $where[] = ['aid','=',$aid];
                    $where[] = ['status','=',1];
                    if($v['params']['category']){
						$cid = intval($v['params']['category']);
						$chidlc = Db::name('article_category')->where('aid',$aid)->where('pid',$cid)->select()->toArray();
						if($chidlc){
							$cids = array($cid);
							foreach($chidlc as $c){
								$cids[] = intval($c['id']);
							}
							$where[] = ['cid','in',$cids];
						}else{
							$where[] = ['cid','=',$cid];
						}
					}
					if($v['params']['bid']!=='' && $v['params']['bid']!==null){
						$where[] = ['bid','=',$v['params']['bid']];
					}
					if($v['params']['group']){
						$_string = array();
						foreach($v['params']['group'] as $gid=>$istrue){
							if($istrue=='true'){
								if($gid == '0'){
									$_string[] = "gid is null or gid=''";
								}else{
									$_string[] = "find_in_set({$gid},gid)";
								}
							}
						}
						if(!$_string){
							$where2 = '0=1';
						}else{
							$where2 = implode(" or ",$_string);
						}
					}else{
						$where2 = '1=1';
					}
					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'sendtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'sendtime') $order = 'createtime';
					if($v['params']['sortby'] == 'readcount') $order = 'readcount desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
					$field = 'id artid,name,subname,pic,author,readcount,sort,createtime,sendtime';
                    $result = Db::name('article')->field($field)->where($where)->where($where2)->order($order)->limit($v['params']['shownum'])->select()->toArray();
					if(!$result) $result = array();
					foreach($result as $k2=>$v2){
						$result[$k2]['sendtime'] = date('Y-m-d',$v2['createtime']);
						$result[$k2]['createtime'] = date('Y-m-d',$v2['createtime']);
						$result[$k2]['id'] = 'G'.time().rand(10000000,99999999);
						}
					$pagecontent[$k]['data'] = $result;
				}
			}elseif($v['temp'] == 'holiday'){//节日倒计时 获取节日信息
				$where = [];
				$where[] = ['aid','=',$aid];
				$where[] = ['status','=',1];
				$where[] = ['holiday_date','>=',strtotime(date('Y-m-d'))]; // 只显示今天及以后的节日

				$order = 'holiday_date asc'; // 默认按日期升序排列
				if($v['params']['sortby'] == 'importance') $order = 'importance desc,holiday_date asc';
				if($v['params']['sortby'] == 'createtime') $order = 'createtime desc';

				$shownum = intval($v['params']['shownum']) ?: 5;

				$result = Db::name('holiday')
					->field('id,name,holiday_date,description,icon,importance,createtime')
					->where($where)
					->order($order)
					->limit($shownum)
					->select()
					->toArray();

				if(!$result) $result = array();

				$now = time();
				foreach($result as $k2=>$v2){
					$result[$k2]['holiday_date_format'] = date('Y-m-d', $v2['holiday_date']);
					$result[$k2]['holiday_date_text'] = date('m月d日', $v2['holiday_date']);

					// 计算倒计时天数
					$days_diff = ceil(($v2['holiday_date'] - $now) / 86400);
					if($days_diff == 0){
						$result[$k2]['countdown_text'] = '今天';
						$result[$k2]['countdown_days'] = 0;
					} else if($days_diff == 1){
						$result[$k2]['countdown_text'] = '明天';
						$result[$k2]['countdown_days'] = 1;
					} else {
						$result[$k2]['countdown_text'] = '还有' . $days_diff . '天';
						$result[$k2]['countdown_days'] = $days_diff;
					}

					// 设置默认图标
					if(empty($v2['icon'])){
						$result[$k2]['icon'] = PRE_URL.'/static/img/holiday-default.png';
					}

					// 生成唯一ID
					$result[$k2]['id'] = 'H'.time().rand(10000000,99999999);
				}

				$pagecontent[$k]['data'] = $result;
			}elseif($v['temp'] == 'coupon'){//优惠券
				if($v['params']['couponfrom'] == 0){//手动选择
					$newdata = array();
					foreach($v['data'] as $cp){
						$newcp = Db::name('coupon')->field('id couponid,type,limit_count,price,name,money,minprice,starttime,endtime,score')->where('aid',$aid)->where('id',$cp['couponid'])->find();
						if($newcp){
							$newart['id'] = $cp['id'];
							$newdata[] = $newcp;
						}
					}
					$pagecontent[$k]['data'] = $newdata;
				}else{
					$time = time();
					$where = "aid={$aid} and unix_timestamp(starttime)<={$time} and unix_timestamp(endtime)>={$time}";
					if($v['params']['bid']!=='' && $v['params']['bid']!==null){
						$where .= ' and bid='.$v['params']['bid'];
					}
					$order = 'sort desc';
					if($v['params']['sortby'] == 'sort') $order = 'sort desc,id desc';
					if($v['params']['sortby'] == 'createtimedesc') $order = 'createtime desc';
					if($v['params']['sortby'] == 'createtime') $order = 'createtime';
					if($v['params']['sortby'] == 'stock') $order = 'stock desc,sort desc';
					if($v['params']['sortby'] == 'rand') $order = Db::raw('rand()');
					$result = Db::name('coupon')->field('id couponid,type,limit_count,price,name,money,minprice,starttime,endtime,score')->where($where)->order($order)->limit($v['params']['shownum'])->select()->toArray();
					if(!$result) $result = array();
					foreach($result as $k2=>$v2){
						$result[$k2]['id'] = 'G'.time().rand(10000000,99999999);
					}
					$pagecontent[$k]['data'] = $result;
				}
			}elseif($v['temp'] == 'form'){//表单信息
				$formdata = Db::name('form')->where('aid',$aid)->where('id',$v['data']['id'])->find();
				if($formdata){
					$formcontent = json_decode($formdata['content'],true);
					if($v['params']['isquery'] == '1'){
						$newformdata = [];
						foreach($formcontent as $fk=>$fv){
							if($fv['query'] == '1'){
								$fv['val3'] = '0';
								$newformdata[] = $fv;
							}
						}
						$formcontent = $newformdata;
						$formdata['payset'] = '0';
					}
					$formdata['content'] = $formcontent;
					//表单支付明细
                    }else{
					$formdata = '';
				}
				$pagecontent[$k]['data'] = $formdata;
				if($mid != -1 && strtotime($formdata['starttime']) > time() && $v['params']['wkstpis']){
					die(jsonEncode(['status'=>-4,'msg'=>$v['params']['wkstpis']]));
				}
            }elseif($v['temp'] == 'form-log' && getcustom('form_log_plug')){//表单信息
                $formdata = Db::name('form')->where('aid',$aid)->where('id',$v['data']['id'])->field('id,name')->find();
                if($formdata){
                    $formdata['count'] = 0 + \db('form_order')->where('formid',$formdata['id'])->count();
                    $log = \db('form_order')->where('formid',$formdata['id'])->order('id', 'desc')->field('id,mid,createtime')->find();
                    if($log){
                        $logmember = \db('member')->where('id',$log['mid'])->field('nickname,realname')->find();
                        $log['nickname'] = $logmember['nickname'];
                        $log['realname'] = $logmember['realname'];
                        $log['time'] = date('Y-m-d H:i',$log['createtime']);
                    }

                    $formdata['log'] = $log;
                }else{
                    $formdata = ['count'=>0,'name'=>'','log'=>[]];
                }
                $pagecontent[$k]['data'] = $formdata;
			}elseif($v['temp'] == 'menu'){//按钮导航
				$data = $v['data'];
				$newdata = [];
				if(!$v['params']['pernum']){
					$v['params']['pernum'] = 10;
					$pagecontent[$k]['params']['pernum'] = 10;
				}
				$pagecount = ceil(count($data)/$v['params']['pernum']);
				if(!$pagecount) $pagecount = 1;
				for($i=0;$i<$pagecount;$i++){
					$newdata[$i]=array_slice($data,$v['params']['pernum']*$i,$v['params']['pernum']);
				}
				if($v['params']['showicon']==0){
					$pagecontent[$k]['params']['iconsize'] = 0;
				}
				//dump($newdata);
				$pagecontent[$k]['params']['newdata'] = $newdata;
				$pagecontent[$k]['params']['newdata_linenum'] = ceil($v['params']['pernum']/$v['params']['num']);
			}elseif($v['temp'] == 'shop'){
				if($v['params']['bid'] == 0){
					$shopinfo = Db::name('admin_set')->where('aid',aid)->field('name,logo,desc,tel,kfurl')->find();
				}else{
					$business = Db::name('business')->where('id',$v['params']['bid'])->find();
					$shopinfo = ['name'=>$business['name'],'logo'=>$business['logo'],'desc'=>$business['address'],'tel'=>$business['tel']];
				}
				$pagecontent[$k]['shopinfo'] = $shopinfo;
            }elseif($v['temp'] == 'jidian'){
                $jidian = ['name'=>''];
                if($v['params']['bid'] > 0) {
                    $set = Db::name('jidian_set')->where('aid', aid)->where('bid', $v['params']['bid'])->find();
                    if ($set && $set['status'] == 1) {
                        $jidianNum= self::getOrderNumFromJidian(aid,$v['params']['bid'],$set,$mid);
                        $jidian = [
                            'name' => $set['name'],
                            'bid' => $set['bid'],
                            'reward_name' => $jidianNum['reward_name'],
                            'reward_num' => $jidianNum['reward_num'],
                            'have_num' => $jidianNum['have_num'],
                            'total_num' => $jidianNum['total_num'],
                        ];
                    }
                }
                $pagecontent[$k]['data'] = $jidian;
			}elseif($v['temp'] == 'userinfo'){
                $zhaopin['show_zhaopin']  = 0;
				if($mid > 0){
                    if($v['params']['ordershow']) {
                        $count0 = 0 + Db::name('shop_order')->where('aid',aid)->where('mid',$mid)->where('status',0)->count();
                        $count1 = 0 + Db::name('shop_order')->where('aid',aid)->where('mid',$mid)->where('status',1)->count();
                        $count2 = 0 + Db::name('shop_order')->where('aid',aid)->where('mid',$mid)->where('status',2)->count();
                        $count4 = 0 + Db::name('shop_refund_order')->where('aid',aid)->where('mid',$mid)->where('refund_status',1)->count();
                        $orderinfo = ['count0'=>$count0,'count1'=>$count1,'count2'=>$count2,'count4'=>$count4];
                    }
                    $field = 'id,levelid,nickname,headimg,sex,realname,tel,weixin,aliaccount,country,province,city,area,address,birthday,createtime,bankcardnum,bankname,bankcarduser,money,commission,commission2,score,paypwd,card_id,card_code,aid,pid,ktnum,yqcode';
                    $userinfo = Db::name('member')->field($field)->where('id',$mid)->find();
                    $userinfo['money'] = \app\common\Member::getmoney($userinfo);
                    $userinfo['score'] = \app\common\Member::getscore($userinfo);
                    
                    // 初始化推荐人相关信息为空或默认值
                    $userinfo['referral_name'] = '';
                    $userinfo['referral_avatar'] = PRE_URL.'/static/img/touxiang.png'; // 默认头像
                    $userinfo['referral_detail'] = null;

                    // 查询上级推荐人信息
                    if (!empty($userinfo['pid']) && $userinfo['pid'] > 0) {
                        $referral_field = 'id,nickname,headimg,tel,weixin'; // 根据需要选择字段
                        $referral_user = Db::name('member')->field($referral_field)->where('id', $userinfo['pid'])->find();
                        if ($referral_user) {
                            $userinfo['referral_name'] = $referral_user['nickname'];
                            $userinfo['referral_avatar'] = $referral_user['headimg'] ?: PRE_URL.'/static/img/touxiang.png';
                            // 为了弹窗显示更多信息，可以传递整个推荐人对象或选定字段
                            $userinfo['referral_detail'] = [
                                'nickname' => $referral_user['nickname'],
                                'weixin'   => $referral_user['weixin'],
                                'tel'      => $referral_user['tel']
                                // 您可以根据弹窗需要添加更多字段
                            ];
                        }
                    }
                    
                    // 只有在设置显示时才获取黄积分
                    if(isset($v['params']['huangjifenshow']) && $v['params']['huangjifenshow'] == 1) {
                        $userinfo['scorehuang'] = \app\common\Member::getscorehuang($userinfo);
                    } else {
                        $userinfo['scorehuang'] = 0;
                    }
                    
                    $userinfo['heiscore'] = \app\common\Member::getscoheijifen($userinfo);
                    $userinfo['sunshijine'] = \app\common\Member::getsunshijine($userinfo);
                    $userinfo['contribution_num'] = \app\common\Member::getgongxianzhi($userinfo);
                    $userinfo['syc_num'] = \app\common\Member::getshouyichi($userinfo);
                    
                    // 只有在设置显示时才获取配资
                    if(isset($v['params']['peizishow']) && $v['params']['peizishow'] == 1) {
                        $userinfo['peizi'] = \app\common\Member::getpeizi($userinfo);
                    } else {
                        $userinfo['peizi'] = 0;
                    }
                    
                    $userinfo['guquan'] = \app\common\Member::getguquan($userinfo);
                    
                    // 只有在设置显示时才获取信用分
                    if(isset($v['params']['creditshow']) && $v['params']['creditshow'] == 1) {
                        $userinfo['credit_score'] = \app\common\Member::getcreditscore($userinfo);
                    } else {
                        $userinfo['credit_score'] = 0;
                    }
                    
                    // 获取已入职的职位数量
                    $userinfo['job_count'] = \app\common\Member::getjobcount($userinfo);
                    // 获取分销团队数量
                    $userinfo['team_count'] =  \app\common\Member::getteamcount($userinfo);
                    $userlevel = [];
                    if($v['params']['levelshow']) {
                        $userlevel = Db::name('member_level')->where('aid',aid)->where('id',$userinfo['levelid'])->find();
                        if(!$userlevel) $userlevel = Db::name('member_level')->where('aid',aid)->where('isdefault',1)->find();
                        //扩展等级
                        $userlevelList = [];
                        }
					$sysset = Db::name('admin_set')->where('aid',aid)->field('name,logo,desc,tel,recharge,reg_invite_code,reg_invite_code_type')->find();

					//优惠券数
                    if($v['params']['couponshow']) {
                        $userinfo['couponcount'] = Db::name('coupon_record')->where('aid',aid)->where('mid',$mid)->where('status',0)->where('endtime','>=',time())->count();
                    }
					if($v['params']['formshow']) {
                        $userinfo['formcount'] = Db::name('form_order')->where('aid',aid)->where('mid',$mid)->where('status',1)->count();
                    }
                    //开卡链接
                    if($v['params']['cardshow']) {
                        $membercard = Db::name('membercard')->where('aid',aid)->where('status',1)->order('id desc')->find();
                        $card_returl = $membercard['ret_url'];
                        $card_id = $membercard['card_id'];
                    } else {
                        $card_returl = '';
                        $card_id = '';
                    }
                    
                    // 2025-01-03 22:55:53,565-INFO-[dp-userinfo][parent_show_logic_001] 根据模板参数设置parent_show值
                    $parent_show = isset($v['params']['parent_show']) && $v['params']['parent_show'] == 1 ? true : false;
                    
                    // 2025-01-03 22:55:53,565-INFO-[dp-userinfo][parent_data_init_001] 初始化parent数据，兼容旧版显示方式
                    $parent = null;
                    if (!empty($userinfo['pid']) && $userinfo['pid'] > 0) {
                        $parent_field = 'id,nickname,headimg,tel,weixin';
                        $parent = Db::name('member')->field($parent_field)->where('id', $userinfo['pid'])->find();
                    }
                    
                    // 初始化订单信息
                    if(!isset($orderinfo)) {
                        $orderinfo = [];
                    }
                    
                    }else{
					$userinfo = ['id'=>0,'nickname'=>'未登录','headimg'=>PRE_URL.'/static/img/touxiang.png','money'=>0,'score'=>0,'couponcount'=>0];
					$userlevel = Db::name('member_level')->where('aid',aid)->where('isdefault',1)->find();
					$orderinfo = [];
					$card_returl = '';
					$card_id = '';
                    
                    // 2025-01-03 22:55:53,565-INFO-[dp-userinfo][parent_show_logic_002] 未登录状态也根据配置设置parent_show
                    $parent_show = isset($v['params']['parent_show']) && $v['params']['parent_show'] == 1 ? true : false;
                    
                    // 2025-01-03 22:55:53,565-INFO-[dp-userinfo][parent_data_init_002] 未登录状态parent为null
                    $parent = null;
				}
				$pagecontent[$k]['data'] = ['sysset'=>$sysset,'userinfo'=>$userinfo,'userlevel'=>$userlevel,'userlevelList' => $userlevelList, 'orderinfo'=>$orderinfo,'card_returl'=>$card_returl,'card_id'=>$card_id,'parent' => $parent, 'parent_show'=>$parent_show,'zhaopin'=>$zhaopin];
			}elseif($v['temp'] == 'search'){
                $v['params']['image_search'] = 0;
                
                //添加搜索框必要数据
                //var_dump($pagecontent);
                //$v['params']：后台配置的模板参数
                $admin_set = Db::name('admin_set')->where(['aid'=>aid])->field('area_on,area_set')->find();
                $area_set = [];
                if($admin_set['area_set']) $area_set = json_decode($admin_set['area_set'],true);
                $v['params']['area_config'] = $area_set;
                
                
                if($admin_set['area_on']){
                    
                    //获取所有区域数据
                    $areamode = $area_set['areamode'];
            	    $areas =  Db::name('area')->where(['level'=>intval($areamode)])->select()->toArray();
            	    //按首字母分组
            	    usort($data, function($a, $b) {
                        return strcmp($a['first_letter'], $b['first_letter']);
                    });
                    
                    $groups = [];
                    foreach ($areas as $item) {
                        $initial = $item['first_letter'];
                        if (!isset($groups[$initial])) {
                            $groups[$initial] = [];
                        }
                        $groups[$initial][] = $item;
                    }
            	    ksort($groups);
            	    $v['params']['areas'] = $groups;
                    
                    //if(isset($v['params']['areamode']))unset($v['params']['areamode']);
                    //if(isset($v['params']['areamode_str']))unset($v['params']['areamode_str']);
                    //模板配置ID，需要根据这个查询商户，商品权限
                    $v['params']['template_setting_id'] = template_setting_id;
                    
                    
                    //像前端输出当前区域ID和名称
                    $area_id = input('param.area_id/d');
                    if(!$area_id){
                        $area_id = $_POST['area_id'];
                        
                        $error = '未提交$area_id';
                        if(!$area_id)$error = '经纬度未提交或提交数据异常，也许搞反了';
                        
                    }
                    if($area_id){
                        $v['params']['current_area_id'] = $area_id;
                        $v['params']['current_area_name'] = Db::name('area')->where(['id'=>$area_id])->value('name');
                    }else{
                        $v['params']['current_area_id'] = 0;
                        $v['params']['current_area_name'] = $error;
                    }
                    
                }
                
                
                $pagecontent[$k]['params'] = $v['params'];
			}elseif($v['temp'] == 'supervipcard'){
            	$userinfo = Db::name('member')->field($field)->where('id',$mid)->find();

            	// 检查多商户用户余额数据表是否存在
            	$tableName = config('database.connections.mysql.prefix') . 'business_member';
            	$is_exist = Db::query('SHOW TABLES LIKE "'. $tableName .'"');
            	if($is_exist){
            		$money =  Db::name('business_member')->field("totalmoney")->where('mid', $mid)->count();
            	}else{
            		$money = \app\common\Member::getmoney($userinfo);
            	}

            	$score = \app\common\Member::getscore($userinfo);
            	$coupon = Db::name('coupon_record')->where('aid',aid)->where('mid',$mid)->where('status',0)->where('endtime','>=',time())->count();

            	foreach ($pagecontent[$k]['data'] as $key => $value) {
            		switch ($value['id']) {
            			case 'VIP000000000001':
            				$pagecontent[$k]['data'][$key]['value'] = $money;
            				break;
            			case 'VIP000000000002':
            				$pagecontent[$k]['data'][$key]['value'] = $score;
            				break;
            			case 'VIP000000000003':
            				$pagecontent[$k]['data'][$key]['value'] = $coupon;
            				break;
            		}
            	}
            }elseif($v['temp'] == 'referral'){
                // 我的推荐人组件
                if($mid > 0){
                    // 获取用户信息
                    $field = 'id,nickname,headimg,levelid,pid';
                    $userinfo = Db::name('member')->field($field)->where('id',$mid)->find();
                    
                    // 初始化推荐人信息
                    $referral_info = [
                        'referral_id' => 0,
                        'referral_name' => '',
                        'referral_avatar' => PRE_URL.'/static/img/touxiang.png',
                        'referral_level' => '普通会员'
                    ];
                    
                    // 查询推荐人信息
                    if(!empty($userinfo['pid']) && $userinfo['pid'] > 0){
                        $referral_field = 'id,nickname,headimg,levelid,tel,weixin';
                        $referral_user = Db::name('member')->field($referral_field)->where('id', $userinfo['pid'])->find();
                        
                        if($referral_user){
                            $referral_info['referral_id'] = $referral_user['id'];
                            $referral_info['referral_name'] = $referral_user['nickname'];
                            $referral_info['referral_avatar'] = $referral_user['headimg'] ?: PRE_URL.'/static/img/touxiang.png';
                            
                            // 获取推荐人等级信息
                            $referral_level = Db::name('member_level')->where('aid',aid)->where('id',$referral_user['levelid'])->find();
                            if($referral_level){
                                $referral_info['referral_level'] = $referral_level['name'];
                            }
                            
                            // 保存联系方式
                            $referral_info['referral_tel'] = $referral_user['tel'] ?: '';
                            $referral_info['referral_weixin'] = $referral_user['weixin'] ?: '';
                        }
                    }
                }else{
                    // 未登录状态
                    $referral_info = [
                        'referral_id' => 0,
                        'referral_name' => '',
                        'referral_avatar' => PRE_URL.'/static/img/touxiang.png',
                        'referral_level' => '普通会员'
                    ];
                }
                
                $pagecontent[$k]['data'] = ['userinfo' => $referral_info];
            }elseif($v['temp'] == 'membercard'){
            	// 会员卡组件
            	if($mid > 0){
            		// 获取会员信息
					$userinfo = Db::name('member')->field('id,nickname,levelid')->where('id',$mid)->find();
					
					// 获取会员等级信息
					$levelinfo = null;
					$is_max_level = false; // 是否最高等级标志
					
					if($userinfo && $userinfo['levelid'] > 0) {
						// 2025-01-03 22:55:53,603-INFO-[member_card_display][get_level_info_001] - 获取会员等级信息包含新增的卡片自定义字段
						$levelinfo = Db::name('member_level')
							->field('id,name,next_level_id,card_main_cover,card_sub_cover,card_primary_color,card_secondary_color,card_description,card_activate_url,card_activate_text,card_upgrade_btn_color,card_upgrade_btn_border_color,card_level_name_color,card_description_color')
							->where('id', $userinfo['levelid'])
							->find();
							
						if($levelinfo) {
							// 2025-01-03 22:55:53,604-INFO-[member_card_display][process_card_data_001] - 处理会员卡自定义数据
							// 设置默认值
							$levelinfo['card_primary_color'] = $levelinfo['card_primary_color'] ?: '';
							$levelinfo['card_secondary_color'] = $levelinfo['card_secondary_color'] ?: '';
							$levelinfo['card_activate_text'] = $levelinfo['card_activate_text'] ?: '立即激活';
							$levelinfo['card_upgrade_btn_color'] = $levelinfo['card_upgrade_btn_color'] ?: '#000000';
							$levelinfo['card_upgrade_btn_border_color'] = $levelinfo['card_upgrade_btn_border_color'] ?: '#333333';
							$levelinfo['card_level_name_color'] = $levelinfo['card_level_name_color'] ?: '';
							$levelinfo['card_description_color'] = $levelinfo['card_description_color'] ?: '';
							
							// 判断是否为最高等级
							if($levelinfo['next_level_id'] == 0 || $levelinfo['next_level_id'] == $levelinfo['id']) {
								$is_max_level = true;
								$levelinfo['is_max_level'] = true;
							} else {
								// 获取下一级别信息
								$nextlevel = Db::name('member_level')
									->field('id,name')
									->where('id', $levelinfo['next_level_id'])
									->find();
									
								if($nextlevel) {
									$levelinfo['next_level_name'] = $nextlevel['name'];
									$levelinfo['is_max_level'] = false;
								} else {
									// 下一级ID存在但找不到对应数据，视为最高级
									$levelinfo['is_max_level'] = true;
								}
							}
							
							// 获取当前等级下的所有会员人数（可用于显示排名）
							$levelinfo['level_member_count'] = Db::name('member')
								->where('aid', aid)
								->where('levelid', $levelinfo['id'])
								->count();
								
							// 获取当前会员在此等级的排名
							if($levelinfo['level_member_count'] > 0) {
								// 这里可以根据具体排名规则调整，默认使用会员ID作为排序依据
								$levelinfo['level_rank'] = Db::name('member')
									->where('aid', aid)
									->where('levelid', $levelinfo['id'])
									->where('id', '<=', $mid)
									->count();
							} else {
								$levelinfo['level_rank'] = 1;
							}
						}
					}
					
					// 获取会员卡信息
					//$card = Db::name('membercard')->where('aid',aid)->where('status',1)->field('id,card_id,title as card_name,bg_color,description,logo')->order('id desc')->find();
					
					if($card){
						// 获取会员卡领取状态
						$card_record = Db::name('membercard_record')->where('aid',aid)->where('mid',$mid)->where('card_id',$card['card_id'])->field('id,status,code')->find();
						
						if($card_record){
							$card['card_status'] = $card_record['status'] == 1 ? 'NORMAL' : 'INACTIVE';
							$card['card_code'] = $card_record['code'];
						}else{
							$card['card_status'] = 'NOT_RECEIVED';
							$card['card_code'] = '';
						}
					}
            	}else{
            		// 未登录状态
            		$userinfo = ['id'=>0,'nickname'=>'未登录','levelid'=>0];
            		$card = null;
					$levelinfo = null;
            	}
            	
				// 2025-01-03 22:55:53,605-INFO-[member_card_display][return_data_001] - 返回包含自定义字段的数据给前端
            	$pagecontent[$k]['data'] = [
            		'userinfo' => $userinfo,
            		'card' => $card,
					'levelinfo' => $levelinfo,
					'is_max_level' => $is_max_level ?? false
            	];
            }

			}
		return json_encode($pagecontent);
	}

	public static function getOrderNumFromJidian($aid,$bid,$set,$mid,$num=0,$giveReward=false)
    {
//        $num = 0;
        $currentReward = [];//当前奖励
        $lastReward = [];
        //存在多笔消费，多个奖励的情况
        //统计下单数量
        $paygive_scene = explode(',',$set['paygive_scene']);
        $set['price_start'] = $set['price_start'] > 0 ? $set['price_start'] : 0;

        $where = [];
        $where[] = ['aid','=',$aid];
        $where[] = ['bid','=',$bid];
        $where[] = ['mid','=',$mid];
        $where[] = ['status','=',3];
        $where[] = ['totalprice','>=',$set['price_start']];
        if($set['days'] > 0)
            $where[] = ['createtime','>=',time()-$set['days']*86400];
        $where[] = ['createtime','between',[$set['starttime'],$set['endtime']]];
        if(in_array('shop',$paygive_scene) && $mid > 0){
            $num_shop = \db('shop_order')->where($where)->count();
        }
//        dd( Db::getLastSql());
        if(in_array('restaurant_shop',$paygive_scene) && $mid > 0){
            $num_restaurant_shop = \db('restaurant_shop_order')->where($where)->count();
        }
        if(in_array('restaurant_takeaway',$paygive_scene) && $mid > 0){
            $num_restaurant_takeaway = \db('restaurant_takeaway_order')->where($where)->count();
        }
        $num = $num + $num_shop + $num_restaurant_shop + $num_restaurant_takeaway;

        $setArr = json_decode($set['set'],true);

        //todo 减去已领奖励的订单（第n轮 N>1）
        $lastkey = count($setArr)-1;
        $num = $num % $setArr[$lastkey]['days'];
        if($num === 0) $num = $setArr[$lastkey]['days'];

        if($setArr){
            foreach ($setArr as $key => $item){
                if($item['coupon_id'] > 0 && $num >= $item['days']) {
                    $currentReward = $item;
                    //发放奖励
                    if($giveReward){
                        $record = \db('jidian_record')->where('aid',$aid)->where('bid',$bid)->where('mid',$mid)->order('id','desc')->find();
                        if(empty($record) || $record['jidian_num'] < $item['days']) {
                            $member = \db('member')->where('aid',$aid)->where('id',$mid)->find();
                            $data = [
                                'aid'=>$aid,
                                'bid'=>$bid,
                                'name' => $set['name'],
                                'mid' => $mid,
                                'headimg'=>$member['headimg'],
                                'nickname' => $member['nickname'],
                                'jidian_num' => $item['days'],
                                'coupon_ids' => $item['coupon_id'],
                                'createtime' => time(),
                                'createdate' => date('Y-m-d'),
                                'status' => 1
                            ];
                            \db('jidian_record')->insert($data);
                            \app\common\Coupon::send($aid,$mid,$item['coupon_id']);
                        }
                    }
                }
                if($item['coupon_id'] > 0 && $item['days'] > 0) {
                    $lastReward = $item;
                }
            }
            if(empty($currentReward)){
                $currentReward = $setArr[0];
            }
            if($lastReward['days'] == $num){
                $currentReward = $setArr[0];
                $num = 0;
            }
        }
        return ['total_num'=>$lastReward['days'],'have_num'=>$num,'reward_name'=>$currentReward['coupon_name'],'reward_num'=>$currentReward['days']];
    }

	/**
     * 计算职位推荐佣金
     * @param array $job 职位信息
     * @param int $mid 会员ID
     * @return array 佣金信息
     */
    public static function calculateCommission($job, $mid = 0) {
        if(empty($job) || $mid <= 0 || $job['reward_type'] == -1) {
            return [];
        }
        
        // 记录计算佣金的日志
        trace(date('Y-m-d H:i:s').'-INFO-[System][calculateCommission_001] 开始计算职位佣金: '.json_encode([
            'position_id' => $job['zwid'] ?? $job['id'] ?? 0,
            'reward_type' => $job['reward_type']
        ], JSON_UNESCAPED_UNICODE), 'info');
        
        // 获取当前会员信息和会员等级
        $member = Db::name('member')
            ->alias('m')
            ->join('member_level l', 'm.levelid=l.id')
            ->where('m.id', $mid)
            ->field('m.id,m.levelid,m.nickname,l.name as level_name,
                    l.can_agent,l.commission1,l.commission2,l.commission3,
                    l.commissiontype')
            ->find();
            
        if (!$member) {
            return [];
        }
        
        // 薪资范围解析
        $salary_range = explode('-', $job['salary']);
        $salary_min = isset($salary_range[0]) ? intval($salary_range[0]) : 0;
        $salary_max = isset($salary_range[1]) ? intval($salary_range[1]) : $salary_min;
        
        $result = [
            'commission_min' => 0,
            'commission_max' => 0,
            'commission_desc' => '元'
        ];
        
        // 根据不同推荐奖励类型计算佣金
        switch ($job['reward_type']) {
            case 0: // 按照会员等级
                if ($member['can_agent'] == 0) {
                    return [];
                }
                
                // 判断会员等级的佣金计算方式
                if ($member['commissiontype'] == 1) { // 固定金额
                    $result['commission_min'] = $member['commission1'];
                    $result['commission_max'] = $member['commission1'];
                } else { // 比例计算
                    // 根据薪资计算佣金
                    $commission_min = round($salary_min * $member['commission1'] * 0.01, 2);
                    $commission_max = round($salary_max * $member['commission1'] * 0.01, 2);
                    
                    $result['commission_min'] = $commission_min;
                    $result['commission_max'] = $commission_max;
                }
                break;
                
            case 1: // 单独设置提成比例
                if ($member['can_agent'] == 0) {
                    return [];
                }
                
                // 解析职位中设置的佣金比例数据
                $reward_data = json_decode($job['reward_data1'], true);
                
                if (!$reward_data || !isset($reward_data[$member['levelid']])) {
                    return [];
                }
                
                // 获取当前会员等级的一级佣金比例
                $commission_rate = isset($reward_data[$member['levelid']]['1']) ? 
                                  $reward_data[$member['levelid']]['1'] : 0;
                
                // 根据薪资范围和佣金比例计算最终佣金
                $commission_min = round($salary_min * $commission_rate * 0.01, 2);
                $commission_max = round($salary_max * $commission_rate * 0.01, 2);
                
                $result['commission_min'] = $commission_min;
                $result['commission_max'] = $commission_max;
                $result['rate'] = $commission_rate;
                break;
                
            case 2: // 单独设置提成金额
                if ($member['can_agent'] == 0) {
                    return [];
                }
                
                // 解析职位中设置的固定金额数据
                $reward_data = json_decode($job['reward_data2'], true);
                
                if (!$reward_data || !isset($reward_data[$member['levelid']])) {
                    return [];
                }
                
                // 获取当前会员等级的一级固定佣金金额
                $commission = isset($reward_data[$member['levelid']]['1']) ? 
                             $reward_data[$member['levelid']]['1'] : 0;
                
                $result['commission_min'] = $commission;
                $result['commission_max'] = $commission;
                break;
        }
        
        // 佣金金额四舍五入到小数点后两位
        $result['commission_min'] = round($result['commission_min'], 2);
        $result['commission_max'] = round($result['commission_max'], 2);
        
        // 记录计算结果
        trace(date('Y-m-d H:i:s').'-INFO-[System][calculateCommission_002] 职位佣金计算结果: '.json_encode([
            'position_id' => $job['zwid'] ?? $job['id'] ?? 0,
            'member_id' => $member['id'],
            'level_id' => $member['levelid'],
            'level_name' => $member['level_name'],
            'result' => $result
        ], JSON_UNESCAPED_UNICODE), 'info');
        
        return $result;
    }
}